/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/Circulars/resources/views/js/create.js ***!
  \********************************************************/
$("#createcirculars_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandlercircular(form, createcircularsRoute.store, 'post', '#createcirculars_form', '#savecirculars', '#newCircularEntry', '#circular_table');
    return false;
  }
});
/******/ })()
;