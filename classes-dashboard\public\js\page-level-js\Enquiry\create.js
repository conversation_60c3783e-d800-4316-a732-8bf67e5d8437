/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Enquiry/resources/views/js/create.js ***!
  \******************************************************/
$("#createenquiry_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandler(form, enquiryCreateRoute.store, 'post', '#createenquiry_form', '#saveenquiry', '#newEnquiryEntry', '#enquiry_table');
    return false;
  }
});
$("#department").change(function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = window.location.origin + checkHost() + '/ajax/classroom';
  params["requestType"] = "GET";
  params["data"] = {
    department: $(this).val()
  };
  params["successCallbackFunction"] = function successCallbackFunction(response) {
    var select = $(".classroom-data");
    select.empty();
    select.append($('<option value="">Select Classroom</option>'));
    $.each(response.classlist, function (index, value) {
      select.append($('<option value="' + value.id + '">' + value.class_name + "</option>"));
    });
  };
  commonAjax(params);
});
/******/ })()
;