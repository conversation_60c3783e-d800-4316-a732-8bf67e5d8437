/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Department/resources/views/js/edit.js ***!
  \*******************************************************/
$("#editdepartment_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    var url = editDepartmentRoute.update;
    ajaxHandler(form, url, 'PATCH', '#editdepartment_form', '#savedepartment', '#newDepartmentEntry', '#department_table');
    return false;
  }
});
/******/ })()
;