/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Resources/resources/views/js/edit.js ***!
  \******************************************************/
$("#editresource_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    var url = editresourceRoute.update;
    ajaxHandler(form, url, 'PATCH', '#editresource_form', '#saveresource', '#newResourceEntry', '#resource_table');
    return false;
  }
});
/******/ })()
;