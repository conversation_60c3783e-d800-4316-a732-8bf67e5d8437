/******/ (() => { // webpackBootstrap
/*!*********************************************************!*\
  !*** ./modules/Enquiry/resources/views/js/feesindex.js ***!
  \*********************************************************/
var columns = [{
  data: 'action',
  name: 'action',
  orderable: false
}, {
  data: 'student_full_name',
  name: 'student_full_name'
}, {
  data: 'enquiry_status',
  name: 'enquiry_status'
}, {
  data: 'payment_date',
  name: 'payment_date'
}, {
  data: 'payment_mode',
  name: 'payment_mode'
}, {
  data: 'paid_amount',
  name: 'paid_amount'
}, {
  data: 'cheque_no',
  name: 'cheque_no'
}, {
  data: 'reference_no',
  name: 'reference_no'
}, {
  data: 'payment_status',
  name: 'payment_status'
}];
var data = function data(d) {
  var params = new URLSearchParams(window.location.search);
  var enquiryId = params.get('enquiry_id');
  d.start_date = $('#start_date').val();
  d.end_date = $('#end_date').val();
  d.enquiry_id = enquiryId;
};
function callBack(res) {
  $('.totalcredit').text(res.total_credit_amount);
}
var table = commonDatatable('#enq_fees_table', feesIndexRoute.index, columns, data, callBack);
function tablescroll() {
  $('html, body').animate({
    scrollTop: $("#enq_fees_table").offset().top
  }, 1000);
}
$("#filter").on('click', function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$('#filterreset').click(function () {
  event.preventDefault();
  $('#start_date').val("");
  $('#end_date').val("");
  tablescroll();
  table.draw();
});
$(document).on("click", ".exportData", function () {
  var params = new URLSearchParams(window.location.search);
  var enquiryId = params.get('enquiry_id');
  var url = feesIndexRoute["export"];
  var data = {
    start_date: $('#start_date').val(),
    end_date: $('#end_date').val(),
    enquiry_id: enquiryId
  };
  exportData(url, data);
});
/******/ })()
;