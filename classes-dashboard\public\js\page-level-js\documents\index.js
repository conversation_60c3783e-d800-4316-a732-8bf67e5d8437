/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Document/resources/views/js/index.js ***!
  \******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "title",
  name: "documents.document_name"
}, {
  data: "file",
  name: "documents.file"
}, {
  data: "category",
  name: "doc_categories.category_name"
}, {
  data: "student_name",
  name: "student_info.first_name"
}, {
  data: "description",
  name: "documents.description"
}, {
  data: "created_by",
  name: "documents.created_by"
}];
var table = commonDatatable("#document_table", documentsRoute.index, columns, data);
function data(d) {
  d.department = $("#department").val();
  d.classroom = $("#classroom-filter").val();
  d.student_id = $("#student-data").val();
}
function tablescroll() {
  $("html, body").animate({
    scrollTop: $("#document_table").offset().top
  }, 1000);
}
$("#filter").on("click", function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$("#filterreset").click(function () {
  event.preventDefault();
  $("#department").val("").trigger("change");
  $("#classroom-filter").val("").trigger("change");
  $("#student-data").val("").trigger("change");
  tablescroll();
  table.draw();
});
$(document).on("click", "#addDocumentEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = documentsRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Document");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteDocumentEntry", function () {
  var documentid = $(this).attr("data-documentid");
  var baseUrl = documentsRoute["delete"];
  var url = baseUrl.replace(":documentid", documentid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;