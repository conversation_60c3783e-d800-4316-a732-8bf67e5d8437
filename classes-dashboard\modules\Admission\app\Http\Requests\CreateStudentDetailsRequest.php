<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'family_name' => 'nullable|string|max:200',
            "first_name"    => "required|string|max:200",
            "middle_name"    => "required|string|max:200",
            "last_name"    => "required|string|max:200",
            "gender"    => "required|string|max:200",
            'aadhaar_no' => 'nullable|numeric|digits_between:1,15',
            'department' => 'required|string',
            'year' => 'required|string',
            'classroom' => 'required|string',

            "blood_group"    => "nullable|string|max:200",
            'address' => 'nullable|string|max:200',
            'city' => 'nullable|string|max:200',
            'pin' => 'nullable|numeric|digits_between:0,10',
            'district' => 'nullable|string|max:200',
            'state' => 'nullable|string|max:200',
            "date_of_birth"    => "nullable|string|max:200",
            "contact_no"    => "required|numeric|digits_between:7,13",
            'email' => 'nullable|email|max:200',

            'country' => 'nullable|string|max:200',
            'religion' => 'nullable|string|max:200',
            'caste' => 'nullable|string|max:200',
            'sub_caste' => 'nullable|string|max:200',
            'gr_no' => 'required|string|min:6|max:200',
            'birth_place' => 'nullable|string|max:200',
            'mother_tongue' => 'nullable|string|max:200',

            // Laravel metadata fields
            'class_uuid' => 'nullable|string',
            'uid_no' => 'nullable|string',
            'student_id' => 'nullable|string',
        ];

        if (request()->hasFile('photo')) {
            $rules = array_merge($rules, [
                'photo' => 'mimes:jpg,jpeg,png|max:4096',
            ]);
        }

        return $rules;
    }
}
