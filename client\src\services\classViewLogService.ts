import { axiosInstance } from '@/lib/axios';

export const logClassView = async (classId: string, studentId: string) => {
  try {
    const response = await axiosInstance.post('/class-view-log/log-view', {
      classId,
      studentId,
    });
    return response.data;
  } catch (error: any) {
    console.error('Error logging class view:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Failed to log class view',
    };
  }
};

export const getClassViewers = async (classId: string, page = 1, limit = 10) => {
  try {
    const response = await axiosInstance.get(`/class-view-log/viewers/${classId}`, {
      params: { page, limit },
    });
    return response.data;
  } catch (error: any) {
    console.error('Error fetching class viewers:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Failed to fetch class viewers',
    };
  }
};
