/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/Resources/resources/views/js/create.js ***!
  \********************************************************/
$("#createresource_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createresourceRoute.store, 'post', '#createresource_form', '#saveresource', '#newResourceEntry', '#resource_table');
    return false;
  }
});
/******/ })()
;