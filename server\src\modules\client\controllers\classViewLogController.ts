import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { logClassView, getClassViewersList } from '../services/studentClassViewService';

export const logClassViewPublicController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId, studentId } = req.body;

    if (!studentId || !classId) {
      return sendError(res, 'Student ID and Class ID are required', 400);
    }

    const result = await logClassView(studentId, classId);

    if (result.success) {
      return sendSuccess(res, result.data, result.message);
    } else {
      return sendError(res, result.message, 500);
    }
  } catch (error: any) {
    console.error('Error logging class view:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

export const getClassViewersController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!classId) {
      return sendError(res, 'Class ID is required', 400);
    }

    const result = await getClassViewersList(classId, page, limit);

    if (result.success) {
      return sendSuccess(res, result.data, 'Class viewers fetched successfully', result.pagination as any );
    } else {
      return sendError(res, result.message, 500);
    }
  } catch (error: any) {
    console.error('Error fetching class viewers:', error);
    return sendError(res, 'Internal server error', 500);
  }
};
