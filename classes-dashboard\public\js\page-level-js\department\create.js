/******/ (() => { // webpackBootstrap
/*!*********************************************************!*\
  !*** ./modules/Department/resources/views/js/create.js ***!
  \*********************************************************/
$("#createdepartment_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createDepartmentRoute.store, 'post', '#createdepartment_form', '#savedepartment', '#newDepartmentEntry', '#department_table');
    return false;
  }
});
/******/ })()
;