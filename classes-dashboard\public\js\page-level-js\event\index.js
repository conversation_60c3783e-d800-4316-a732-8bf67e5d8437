/******/ (() => { // webpackBootstrap
/*!***************************************************!*\
  !*** ./modules/Event/resources/views/js/index.js ***!
  \***************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "event_name",
  name: "event_name"
}, {
  data: "date",
  name: "date"
}];
var table = commonDatatable("#event_table", eventRoute.index, columns);
$(document).on("click", "#addEventEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = eventRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Event");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteEventEntry", function () {
  var did = $(this).attr("data-deleteeventid");
  var url = eventRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).ready(function () {
  $("body").delegate("#event_date", "focusin", function () {
    $(this).datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: false,
      minDate: new Date(startYearDate),
      maxDate: new Date(endYearDate)
    });
  });
});
$(document).on("click", ".editEventEntry", function () {
  var editdid = $(this).attr("data-editeventid");
  var url = eventRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Event");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".exportData", function () {
  var url = eventRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;