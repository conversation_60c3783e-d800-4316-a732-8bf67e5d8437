/******/ (() => { // webpackBootstrap
/*!*****************************************************!*\
  !*** ./modules/Subject/resources/views/js/index.js ***!
  \*****************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "subject_name",
  name: "subject_name"
}, {
  data: "class_info",
  name: "class_info"
}];
var data = function data(d) {
  d.department = $('#department').val();
  d.classroom = $('#classroom-filter').val();
};
var table = commonDatatable("#subject_table", subjectRoute.index, columns, data);
var currentUrl = window.location.href;
var parts = currentUrl.split('/');
var slug = parts.pop();
function tablescroll() {
  $('html, body').animate({
    scrollTop: $("#subject_table").offset().top
  }, 1000);
}
$("#filter").on('click', function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$('#filterreset').click(function () {
  event.preventDefault();
  $('#department').val("").trigger('change');
  $('#classroom-filter').val("").trigger('change');
  tablescroll();
  table.draw();
});
$(document).on("click", "#addSubjectEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = subjectRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Subject");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editsubjectEntry", function () {
  var editdid = $(this).attr("data-editsubjectid");
  var url = subjectRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Subject");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteSubjectEntry", function () {
  var did = $(this).attr("data-deletesubjectid");
  var url = subjectRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on("change", "#department", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = window.location.origin + checkHost() + '/ajax/classroom';
  params["requestType"] = "GET";
  params["data"] = {
    department: $(this).val()
  };
  params["successCallbackFunction"] = function successCallbackFunction(response) {
    var select = $(".classroom-data");
    select.empty();
    select.append($('<option value="">Select Classroom</option>'));
    $.each(response.classlist, function (index, value) {
      select.append($('<option value="' + value.id + '">' + value.class_name + "</option>"));
    });
  };
  commonAjax(params);
});
$(document).on("click", ".exportData", function () {
  var url = subjectRoute["export"];
  var data = {
    department: $('#department').val(),
    classroom: $('#classroom-filter').val()
  };
  exportData(url, data);
});
/******/ })()
;