/******/ (() => { // webpackBootstrap
/*!************************************************************!*\
  !*** ./modules/AnnualCalendar/resources/views/js/index.js ***!
  \************************************************************/
var calendarEl = document.getElementById('calendar');
var calendar = new FullCalendar.Calendar(calendarEl, {
  headerToolbar: {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,listMonth'
  },
  height: "auto",
  events: {
    url: calenderRoute.index,
    cache: true
  },
  eventDidMount: function eventDidMount(arg) {
    var cs = document.querySelectorAll(".cs");
    cs.forEach(function (v) {
      if (v.checked) {
        if (arg.event.extendedProps.cid === v.value) {
          arg.el.style.display = "block";
        }
      } else {
        if (arg.event.extendedProps.cid === v.value) {
          arg.el.style.display = "none";
        }
      }
    });
  },
  loading: function loading(bool) {
    $('.page-loader').toggle();
  }
});
calendar.render();
var csx = document.querySelectorAll(".cs");
csx.forEach(function (el) {
  el.addEventListener("change", function () {
    calendar.refetchEvents();
  });
});
/******/ })()
;