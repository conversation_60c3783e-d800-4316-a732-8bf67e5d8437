import express from 'express';
import {
  logClassViewPublicController,
  getClassViewersController,
} from '../controllers/classViewLogController';
import { authClientMiddleware } from '@/middlewares/clientAuth';

const classViewLogRoutes = express.Router();

classViewLogRoutes.post('/log-view', logClassViewPublicController);
classViewLogRoutes.get('/viewers/:classId', authClientMiddleware, getClassViewersController);

export default classViewLogRoutes;
