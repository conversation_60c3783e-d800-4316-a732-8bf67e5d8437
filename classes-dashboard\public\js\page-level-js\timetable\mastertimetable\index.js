/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Timetable/resources/views/js/index.js ***!
  \*******************************************************/
$(document).on('click', '#addCT', function () {
  var params = $.extend({}, doAjax_params_default);
  params['url'] = masterTimetableRoute.create;
  params['requestType'] = "GET";
  params['successCallbackFunction'] = function successCallbackFunction(result) {
    $('#modeltitle').html('Assign Timeslot to Classroom');
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on('click', '.addsub', function () {
  var params = $.extend({}, doAjax_params_default);
  params['url'] = masterTimetableRoute.subjectforclass;
  params['requestType'] = "GET";
  params['data'] = 'classroom=' + $(this).attr('data-classid') + '&slotid=' + $(this).attr('data-slotid');
  params['successCallbackFunction'] = function successCallbackFunction(result) {
    $('#modeltitle').html('Assign Subject');
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$('#department_classroom').change(function () {
  var params = $.extend({}, doAjax_params_default);
  params['url'] = masterTimetableRoute.getclassroomlist;
  params['requestType'] = "GET";
  params['data'] = {
    department: $(this).val()
  };
  params['successCallbackFunction'] = function successCallbackFunction(response) {
    var select = $('.classroom-data');
    select.empty();
    select.append($('<option value="">All</option>'));
    $.each(response.classlist, function (index, value) {
      select.append($('<option value="' + value.id + '">' + value.class_name + '</option>'));
    });
  };
  commonAjax(params);
});
/******/ })()
;