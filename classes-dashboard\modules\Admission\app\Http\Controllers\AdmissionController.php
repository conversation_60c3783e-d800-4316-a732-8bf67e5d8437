<?php

namespace Admission\Http\Controllers;

use Admission\Http\Requests\CreateLeavingCertificateRequest;
use Admission\Http\Requests\CreateStudentDetailsRequest;
use Admission\Http\Requests\CreateStudentParentsDetailsRequest;
use Admission\Http\Requests\CreateStudentWaypointRequest;
use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\ClassroomWiseFeeRepository;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use StudentAttendance\Repositories\AttendanceRepository;
use Years\Repositories\YearsRepository;

class AdmissionController extends Controller
{
    protected $admissionRepository;
    protected $yearsRepository;
    protected $classroomWiseFeeRepository;
    protected $attendanceRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        YearsRepository $yearsRepository,
        ClassroomWiseFeeRepository $classroomWiseFeeRepository,
        AttendanceRepository $attendanceRepository
    ) {
        $this->middleware('permission:read student', ['only' => ['index', 'show']]);
        $this->middleware('permission:create student', ['only' => ['create', 'storeStudentDetails', 'storeStudentParentsDetails']]);
        $this->middleware('permission:update student', ['only' => ['edit', 'storeStudentDetails', 'storeStudentParentsDetails']]);
        $this->middleware('permission:manage student exit', ['only' => ['generateLeavingCertificate']]);
        $this->admissionRepository = $admissionRepository;
        $this->yearsRepository = $yearsRepository;
        $this->classroomWiseFeeRepository = $classroomWiseFeeRepository;
        $this->attendanceRepository = $attendanceRepository;
    }

    public function index(Request $request)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();

        if (request()->ajax()) {
            $list = $this->admissionRepository->getAll($request);
            return $this->admissionRepository->getDatatable($list);
        }
        return view('Admission::index', compact("department", "years"));
    }

    public function create()
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        return view('Admission::create', compact('department', 'years'));
    }

    public function storeStudentDetails(CreateStudentDetailsRequest $request)
    {
        try {
            Log::info('Laravel storeStudentDetails called with data:', $request->all());

            // Check if GR No already exists
            if ($this->admissionRepository->checkGRNO($request->gr_no, $request->year, $request->student_id)) {
                return response()->json(['error' => 'GR No Already Exist for student!!']);
            }

            // Store only metadata in Laravel (class_uuid, gr_no, uid_no)
            // The student_id comes from Node.js after creating student profile
            $data = [
                'class_uuid' => $request->class_uuid,
                'gr_no' => $request->gr_no,
                'uid_no' => $request->uid_no,
                'student_id' => $request->student_id, // UUID from Node.js
                'year' => $request->year,
            ];

            Log::info('Data to be stored in Laravel:', $data);

            $id = $this->admissionRepository->storeStudentDetails($data);

            Log::info('Laravel metadata stored successfully with ID:', ['id' => $id]);

            return response()->json(['success' => 'Student metadata stored successfully in Laravel!', 'id' => $id]);

        } catch (\Exception $e) {
            Log::error('Error in storeStudentDetails: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save student metadata: ' . $e->getMessage()]);
        }
    }

    public function storeStudentParentsDetails(CreateStudentParentsDetailsRequest $request)
    {
        // Parent details are now handled directly by frontend API calls to Node.js
        // This endpoint is kept for backward compatibility but not used in new flow
        $this->admissionRepository->storeStudentParentsDetails($request->all());
        return response()->json(['success' => 'Students Parents Details Added Successfully!!']);
    }



    public function edit($id)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        $data = $this->admissionRepository->findByid($id);
        return view('Admission::create', compact('data', 'department', 'years'));
    }

    public function show($id)
    {
        $year = geActiveYearName();
        $dateData['startDate'] = $year->start_date;
        $dateData['endDate'] = $year->end_date;

        $data = $this->admissionRepository->findByid($id);
        $lcData = $this->admissionRepository->getLeavingCertificate($id);
        $attendance = $this->attendanceRepository->getAttendanceForAPI($dateData, $id);
        ksort($attendance);
        return view('Admission::show', compact('data', 'lcData', 'attendance'));
    }

    public function destroy($id)
    {
        $this->admissionRepository->findByid($id)->delete();
        return response()->json(['success' => 'Student Deleted Successfully']);
    }

    public function getStudentParentDetailsFromContactNo(Request $request)
    {
        $parentdetail = $this->admissionRepository->getStudentParentDetailsFromContactNo($request->all());

        if (isset($parentdetail->getParentInfo)) {
            return response()->json(['success' => 'Parent Details Fetched Successfully', 'parentdetail' => $parentdetail->getParentInfo]);
        } else {
            return response()->json(['success' => 'Parent Details Not Found']);
        }
    }

    public function searchStudent(Request $request)
    {
        $data = $this->admissionRepository->searchStudent($request);
        return $data;
    }

    public function generateLeavingCertificate(CreateLeavingCertificateRequest $request, $student_id)
    {
        $data = $this->admissionRepository->generateLeavingCertificate($request->all(), $student_id);
        return response()->json(['success' => 'Leaving Certificate Generated Successfully']);
    }

    public function getLeavingCertificate($id)
    {
        $data = $this->admissionRepository->getLeavingCertificate($id);
        return view('Admission::lc', compact('data'));
    }

    public function activeInactive($id)
    {
        $this->admissionRepository->activeInactive($id);
        return redirect()->back()->with('success', 'Student Status Updated successfully!!');
    }

    public function exportStudents(Request $request) 
    {    
        $students = $this->admissionRepository->getAll($request)->get();
        return commonExport($students, 'Admission::export', 'students');
    }

    public function getStudentsByClassroom(Request $request) 
    {    
        $students = $this->admissionRepository->getAllStudentByClassAndDepartment($request->all());
        return response()->json(array('students' => $students));
    }

    public function checkGRNO(Request $request) 
    {    
        $data = $request->all();
        $isGRExists = $this->admissionRepository->checkGRNO($data['gr_no'], getActiveYearId(), $data['student_id']);
        if ($isGRExists) {
            return response()->json(['error' => 'GRNO already exist']);
        }
    }

    public function saveRoutesToStudent(CreateStudentWaypointRequest $request, $student_id)
    {
        $data = $request->validated();
        $this->admissionRepository->logWaypointRouteChange($student_id, $data);
        $this->admissionRepository->updateRoutes($data, $student_id);
        return response()->json(['success' => 'Waypoint and Route Updated Successfully!!']);
    }
}