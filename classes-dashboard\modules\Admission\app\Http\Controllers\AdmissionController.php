<?php

namespace Admission\Http\Controllers;

use Admission\Http\Requests\CreateLeavingCertificateRequest;
use Admission\Http\Requests\CreateStudentDetailsRequest;
use Admission\Http\Requests\CreateStudentParentsDetailsRequest;
use Admission\Http\Requests\CreateStudentWaypointRequest;
use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\ClassroomWiseFeeRepository;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use StudentAttendance\Repositories\AttendanceRepository;
use Years\Repositories\YearsRepository;

class AdmissionController extends Controller
{
    protected $admissionRepository;
    protected $yearsRepository;
    protected $classroomWiseFeeRepository;
    protected $attendanceRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        YearsRepository $yearsRepository,
        ClassroomWiseFeeRepository $classroomWiseFeeRepository,
        AttendanceRepository $attendanceRepository
    ) {
        $this->middleware('permission:read student', ['only' => ['index', 'show']]);
        $this->middleware('permission:create student', ['only' => ['create', 'storeStudentDetails', 'storeStudentParentsDetails']]);
        $this->middleware('permission:update student', ['only' => ['edit', 'storeStudentDetails', 'storeStudentParentsDetails']]);
        $this->middleware('permission:manage student exit', ['only' => ['generateLeavingCertificate']]);
        $this->admissionRepository = $admissionRepository;
        $this->yearsRepository = $yearsRepository;
        $this->classroomWiseFeeRepository = $classroomWiseFeeRepository;
        $this->attendanceRepository = $attendanceRepository;
    }

    public function index(Request $request)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();

        if (request()->ajax()) {
            $list = $this->admissionRepository->getAll($request);
            return $this->admissionRepository->getDatatable($list);
        }
        return view('Admission::index', compact("department", "years"));
    }

    public function create()
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        return view('Admission::create', compact('department', 'years'));
    }

    public function storeStudentDetails(CreateStudentDetailsRequest $request)
    {
        try {
            // Check if GR No already exists
            if ($this->admissionRepository->checkGRNO($request->gr_no, $request->year, $request->student_id)) {
                return response()->json(['error' => 'GR No Already Exist for student!!']);
            }

            // Step 1: Send student details to Node.js API
            $nodeApiUrl = env('UEST_FRONTEND_URL') . '/api/v1/student-profile/admission/step-one';

            $studentData = [
                'first_name' => $request->first_name,
                'middle_name' => $request->middle_name,
                'last_name' => $request->last_name,
                'family_name' => $request->family_name,
                'gender' => $request->gender,
                'contact_no' => $request->contact_no,
                'email' => $request->email,
                'age' => $request->age,
                'date_of_birth' => $request->date_of_birth,
                'aadhaar_no' => $request->aadhaar_no,
                'blood_group' => $request->blood_group,
                'birth_place' => $request->birth_place,
                'mother_tongue' => $request->mother_tongue,
                'address' => $request->address,
                'city' => $request->city,
                'pin' => $request->pin,
                'district' => $request->district,
                'state' => $request->state,
                'country' => $request->country,
                'religion' => $request->religion,
                'caste' => $request->caste,
                'sub_caste' => $request->sub_caste,
                'gr_no' => $request->gr_no,
                'uid_no' => $request->uid_no,
                'year' => $request->year,
                'department' => $request->department,
                'classroom' => $request->classroom,
            ];

            // Make API call to Node.js
            $nodeResponse = $this->makeNodeApiCall($nodeApiUrl, $studentData, $request->file('photo'));

            if (!$nodeResponse['success']) {
                return response()->json(['error' => 'Failed to save student details: ' . $nodeResponse['message']]);
            }

            $studentId = $nodeResponse['data']['student']['id'];

            // Step 2: Store metadata in Laravel
            $laravelData = [
                'class_uuid' => $request->classroom,
                'gr_no' => $request->gr_no,
                'uid_no' => $request->uid_no,
                'student_id' => $studentId,
                'year' => $request->year,
            ];

            $id = $this->admissionRepository->storeStudentDetails($laravelData);

            return response()->json([
                'success' => 'Student details saved successfully in both Node.js and Laravel!',
                'id' => $id,
                'student_id' => $studentId,
                'node_response' => $nodeResponse
            ]);

        } catch (\Exception $e) {
            \Log::error('Error in storeStudentDetails: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save student details: ' . $e->getMessage()]);
        }
    }

    public function storeStudentParentsDetails(CreateStudentParentsDetailsRequest $request)
    {
        try {
            // Send parent details to Node.js API
            $nodeApiUrl = env('UEST_FRONTEND_URL') . '/api/v1/student-profile/admission/parent-details';

            $parentData = [
                'student_id' => $request->student_id,
                'fathers_name' => $request->fathers_name,
                'fathers_middle_name' => $request->fathers_middle_name,
                'fathers_last_name' => $request->fathers_last_name,
                'fathers_qualification' => $request->fathers_qualification,
                'fathers_occupation' => $request->fathers_occupation,
                'fathers_aadhaar_no' => $request->fathers_aadhaar_no,
                'mothers_name' => $request->mothers_name,
                'mothers_middle_name' => $request->mothers_middle_name,
                'mothers_last_name' => $request->mothers_last_name,
                'mothers_qualification' => $request->mothers_qualification,
                'mothers_occupation' => $request->mothers_occupation,
                'mothers_aadhaar_no' => $request->mothers_aadhaar_no,
                'contact_no_1' => $request->contact_no_1,
                'contact_no_2' => $request->contact_no_2,
                'family_income' => $request->family_income,
                'part_of_ngo' => $request->part_of_ngo,
            ];

            // Make API call to Node.js
            $nodeResponse = $this->makeNodeApiCall($nodeApiUrl, $parentData);

            if (!$nodeResponse['success']) {
                return response()->json(['error' => 'Failed to save parent details: ' . $nodeResponse['message']]);
            }

            // Store parent details in Laravel (if needed)
            $this->admissionRepository->storeStudentParentsDetails($request->all());

            return response()->json([
                'success' => 'Parent details saved successfully in both Node.js and Laravel!',
                'node_response' => $nodeResponse
            ]);

        } catch (\Exception $e) {
            \Log::error('Error in storeStudentParentsDetails: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to save parent details: ' . $e->getMessage()]);
        }
    }

    /**
     * Make API call to Node.js server
     */
    private function makeNodeApiCall($url, $data, $file = null)
    {
        try {
            $client = new \GuzzleHttp\Client();

            $options = [
                'timeout' => 30,
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ];

            if ($file) {
                // Multipart request with file
                $options['multipart'] = [];

                // Add all data fields
                foreach ($data as $key => $value) {
                    $options['multipart'][] = [
                        'name' => $key,
                        'contents' => $value
                    ];
                }

                // Add file if present
                if ($file && $file->isValid()) {
                    $options['multipart'][] = [
                        'name' => 'photo',
                        'contents' => fopen($file->getPathname(), 'r'),
                        'filename' => $file->getClientOriginalName(),
                    ];
                }
            } else {
                // JSON request
                $options['json'] = $data;
            }

            $response = $client->post($url, $options);
            $responseBody = json_decode($response->getBody()->getContents(), true);

            \Log::info('Node.js API Response: ', $responseBody);

            return $responseBody;

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \Log::error('Node.js API Error: ' . $e->getMessage());

            if ($e->hasResponse()) {
                $errorResponse = json_decode($e->getResponse()->getBody()->getContents(), true);
                return [
                    'success' => false,
                    'message' => $errorResponse['message'] ?? 'API request failed',
                    'error' => $errorResponse
                ];
            }

            return [
                'success' => false,
                'message' => 'Failed to connect to Node.js server: ' . $e->getMessage()
            ];
        } catch (\Exception $e) {
            \Log::error('Unexpected error in makeNodeApiCall: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Unexpected error: ' . $e->getMessage()
            ];
        }
    }

    public function edit($id)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        $data = $this->admissionRepository->findByid($id);
        return view('Admission::create', compact('data', 'department', 'years'));
    }

    public function show($id)
    {
        $year = geActiveYearName();
        $dateData['startDate'] = $year->start_date;
        $dateData['endDate'] = $year->end_date;

        $data = $this->admissionRepository->findByid($id);
        $lcData = $this->admissionRepository->getLeavingCertificate($id);
        $attendance = $this->attendanceRepository->getAttendanceForAPI($dateData, $id);
        ksort($attendance);
        return view('Admission::show', compact('data', 'lcData', 'attendance'));
    }

    public function destroy($id)
    {
        $this->admissionRepository->findByid($id)->delete();
        return response()->json(['success' => 'Student Deleted Successfully']);
    }

    public function getStudentParentDetailsFromContactNo(Request $request)
    {
        $parentdetail = $this->admissionRepository->getStudentParentDetailsFromContactNo($request->all());

        if (isset($parentdetail->getParentInfo)) {
            return response()->json(['success' => 'Parent Details Fetched Successfully', 'parentdetail' => $parentdetail->getParentInfo]);
        } else {
            return response()->json(['success' => 'Parent Details Not Found']);
        }
    }

    public function searchStudent(Request $request)
    {
        $data = $this->admissionRepository->searchStudent($request);
        return $data;
    }

    public function generateLeavingCertificate(CreateLeavingCertificateRequest $request, $student_id)
    {
        $data = $this->admissionRepository->generateLeavingCertificate($request->all(), $student_id);
        return response()->json(['success' => 'Leaving Certificate Generated Successfully']);
    }

    public function getLeavingCertificate($id)
    {
        $data = $this->admissionRepository->getLeavingCertificate($id);
        return view('Admission::lc', compact('data'));
    }

    public function activeInactive($id)
    {
        $this->admissionRepository->activeInactive($id);
        return redirect()->back()->with('success', 'Student Status Updated successfully!!');
    }

    public function exportStudents(Request $request) 
    {    
        $students = $this->admissionRepository->getAll($request)->get();
        return commonExport($students, 'Admission::export', 'students');
    }

    public function getStudentsByClassroom(Request $request) 
    {    
        $students = $this->admissionRepository->getAllStudentByClassAndDepartment($request->all());
        return response()->json(array('students' => $students));
    }

    public function checkGRNO(Request $request) 
    {    
        $data = $request->all();
        $isGRExists = $this->admissionRepository->checkGRNO($data['gr_no'], getActiveYearId(), $data['student_id']);
        if ($isGRExists) {
            return response()->json(['error' => 'GRNO already exist']);
        }
    }

    public function saveRoutesToStudent(CreateStudentWaypointRequest $request, $student_id)
    {
        $data = $request->validated();
        $this->admissionRepository->logWaypointRouteChange($student_id, $data);
        $this->admissionRepository->updateRoutes($data, $student_id);
        return response()->json(['success' => 'Waypoint and Route Updated Successfully!!']);
    }
}