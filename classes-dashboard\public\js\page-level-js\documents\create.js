/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Document/resources/views/js/create.js ***!
  \*******************************************************/
$(document).ready(function () {
  $("#createDocuments_form").submit(function (event) {
    event.preventDefault();
    var form = $(this)[0];
    if ($(this).valid()) {
      ajaxHandlercircular(form, createdocumentsRoute.store, 'POST', '#createDocuments_form', '#savedocuments', '#newDocumentEntry', '#studentDocumentsTable', true);
      return false;
    }
  });

  // Handle category change for the Others category
  $('#category_id').on('change', function () {
    var selectedValue = $(this).val();
    var $otherCategoryGroup = $('.other-category-group');
    if (selectedValue === '13') {
      var otherCategoryValue = $('#other_category').val() || '';
      if (otherCategoryValue.trim() !== '') {
        $otherCategoryGroup.show();
      } else {
        $otherCategoryGroup.hide();
      }
    } else {
      $otherCategoryGroup.hide();
    }
  });
  $('#other_category').on('input', function () {
    var $otherCategoryGroup = $('.other-category-group');
    var selectedValue = $('#category_id').val();
    if (selectedValue === '13') {
      var otherCategoryValue = $(this).val() || '';
      if (otherCategoryValue.trim() !== '') {
        $otherCategoryGroup.show();
      } else {
        $otherCategoryGroup.hide();
      }
    }
  });
});
/******/ })()
;