1752336295O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:18:"Years\Models\Years":31:{s:13:" * connection";s:5:"pgsql";s:5:"table";s:5:"years";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:16;s:9:"year_name";s:9:"2026-2027";s:10:"start_date";s:10:"2026-07-01";s:8:"end_date";s:10:"2027-07-01";s:6:"status";s:6:"ACTIVE";s:10:"class_uuid";s:36:"226eb0ae-7fbe-4a07-b0e5-3208409fbd2c";s:10:"created_at";s:19:"2025-07-05 10:53:43";s:10:"updated_at";s:19:"2025-07-09 08:52:01";s:10:"deleted_at";N;}s:11:" * original";a:9:{s:2:"id";i:16;s:9:"year_name";s:9:"2026-2027";s:10:"start_date";s:10:"2026-07-01";s:8:"end_date";s:10:"2027-07-01";s:6:"status";s:6:"ACTIVE";s:10:"class_uuid";s:36:"226eb0ae-7fbe-4a07-b0e5-3208409fbd2c";s:10:"created_at";s:19:"2025-07-05 10:53:43";s:10:"updated_at";s:19:"2025-07-09 08:52:01";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"year_name";i:1;s:10:"start_date";i:2;s:8:"end_date";i:3;s:6:"status";i:4;s:10:"class_uuid";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:1;O:18:"Years\Models\Years":31:{s:13:" * connection";s:5:"pgsql";s:5:"table";s:5:"years";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:15;s:9:"year_name";s:9:"2025-2026";s:10:"start_date";s:10:"2025-07-01";s:8:"end_date";s:10:"2026-06-30";s:6:"status";s:8:"INACTIVE";s:10:"class_uuid";s:36:"226eb0ae-7fbe-4a07-b0e5-3208409fbd2c";s:10:"created_at";s:19:"2025-07-05 09:46:25";s:10:"updated_at";s:19:"2025-07-09 08:52:01";s:10:"deleted_at";N;}s:11:" * original";a:9:{s:2:"id";i:15;s:9:"year_name";s:9:"2025-2026";s:10:"start_date";s:10:"2025-07-01";s:8:"end_date";s:10:"2026-06-30";s:6:"status";s:8:"INACTIVE";s:10:"class_uuid";s:36:"226eb0ae-7fbe-4a07-b0e5-3208409fbd2c";s:10:"created_at";s:19:"2025-07-05 09:46:25";s:10:"updated_at";s:19:"2025-07-09 08:52:01";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:9:"year_name";i:1;s:10:"start_date";i:2;s:8:"end_date";i:3;s:6:"status";i:4;s:10:"class_uuid";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}