/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/Department/resources/views/js/index.js ***!
  \********************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "name",
  name: "department.name"
}, {
  data: "educational",
  name: "educational"
}];
var table = commonDatatable("#department_table", departmentRoute.index, columns);
$(document).ready(function () {
  $("body").delegate("#shift_start_time", "focusin", function () {
    $(this).datetimepicker({
      format: "HH:mm:00",
      icons: {
        up: "fa fa-chevron-up",
        down: "fa fa-chevron-down"
      }
    });
  });
  $("body").delegate("#shift_end_time", "focusin", function () {
    $(this).datetimepicker({
      format: "HH:mm:00",
      icons: {
        up: "fa fa-chevron-up",
        down: "fa fa-chevron-down"
      }
    });
  });
  $("body").delegate("#shift_half_time", "focusin", function () {
    $(this).datetimepicker({
      format: "HH:mm:00",
      icons: {
        up: "fa fa-chevron-up",
        down: "fa fa-chevron-down"
      }
    });
  });
});
$(document).on("click", "#addDepartmentEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = departmentRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Department");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editdepartmentEntry", function () {
  var editdid = $(this).attr("data-editdepartmentid");
  var url = departmentRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Department");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteDepartmentEntry", function () {
  commonAlert(function () {
    commonAjax($.extend({}, doAjax_params_default, {
      url: departmentRoute["delete"].replace(":did", $(this).attr("data-deletedepartmentid")),
      requestType: "DELETE",
      successCallbackFunction: function successCallbackFunction(result) {
        toastr.success(result.success);
        table.draw();
      }
    }));
  }.bind(this));
});
$(document).on("click", ".exportData", function () {
  var url = departmentRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;