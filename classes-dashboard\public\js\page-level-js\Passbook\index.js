/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Passbook/resources/views/js/index.js ***!
  \******************************************************/
var columns = [{
  data: "pid",
  name: "pid",
  orderable: false
}, {
  data: "name",
  name: "name"
}, {
  data: "source",
  name: "source"
}, {
  data: "credit",
  name: "credit"
}, {
  data: "debit",
  name: "debit"
}, {
  data: "date",
  name: "date"
}, {
  data: "payment_mode",
  name: "payment_mode"
}];
var data = function data(d) {
  d.start_date = $("#start_date").val();
  d.end_date = $("#end_date").val();
  d.source = $("#source").val();
  d.payment_mode = $("#payment_mode").val();
};
function callBack(res) {
  $(".totalcredit").text(res.total_credit_amount);
  $(".totaldebit").text(res.total_debit_amount);
}
var table = commonDatatable("#passbook", passbookRoute.index, columns, data, callBack);
function tablescroll() {
  $("html, body").animate({
    scrollTop: $("#passbook").offset().top
  }, 1000);
}
$("#filter").on("click", function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$("#filterreset").click(function () {
  event.preventDefault();
  $("#start_date").val("");
  $("#end_date").val("");
  $("#source").val("").trigger("change");
  $("#payment_mode").val("").trigger("change");
  tablescroll();
  table.draw();
});
$(document).on("click", ".exportData", function () {
  var url = passbookRoute["export"];
  var tempdata = {
    start_date: $("#start_date").val(),
    end_date: $("#end_date").val(),
    source: $("#source").val(),
    payment_mode: $("#payment_mode").val()
  };
  exportData(url, tempdata);
});
/******/ })()
;