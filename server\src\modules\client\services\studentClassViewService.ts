import prisma from '@/config/prismaClient';

export const logClassView = async (studentId: string, classId: string) => {
  try {
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const existingLog = await prisma.studentClassViewLog.findFirst({
      where: {
        studentId,
        classId,
        viewedAt: {
          gte: twentyFourHoursAgo,
        },
      },
    });

    if (existingLog) {
      return {
        success: true,
        message: 'Already logged in last 24 hours',
        data: existingLog,
      };
    }

    const newLog = await prisma.studentClassViewLog.create({
      data: {
        studentId,
        classId,
      },
    });

    return {
      success: true,
      message: 'Class view logged successfully',
      data: newLog,
    };
  } catch (error) {
    console.error('Error logging class view:', error);
    return {
      success: false,
      message: 'Failed to log class view',
      error,
    };
  }
};

export const getClassViewersList = async (classId: string, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const viewLogs = await prisma.studentClassViewLog.findMany({
      where: { classId },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true,
            createdAt: true,
          },
        },
      },
      orderBy: { viewedAt: 'desc' },
      skip,
      take: limit,
    });

    const total = await prisma.studentClassViewLog.count({
      where: { classId },
    });

    return {
      success: true,
      data: viewLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error('Error fetching class viewers list:', error);
    return {
      success: false,
      message: 'Failed to fetch class viewers list',
      error,
    };
  }
};
