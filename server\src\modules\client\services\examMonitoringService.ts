import prisma from '@/config/prismaClient';
import fs from 'fs';
import path from 'path';

interface PhotoUploadResult {
  success: boolean;
  data?: any;
  error?: string;
}

export const saveExamPhoto = async (
  studentId: string,
  examId: number,
  photoData: string
): Promise<PhotoUploadResult> => {
  try {
    if (!studentId || !examId || !photoData) {
      return { success: false, error: 'Missing required parameters' };
    }

    // Extract base64 data and file extension
    const matches = photoData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
    if (!matches) {
      return { success: false, error: 'Invalid image format' };
    }

    const fileExtension = matches[1];
    const base64Data = matches[2];

    const allowedExtensions = ['jpeg', 'jpg', 'png', 'webp'];
    if (!allowedExtensions.includes(fileExtension.toLowerCase())) {
      return { success: false, error: 'Unsupported image format' };
    }

    const sizeInBytes = (base64Data.length * 3) / 4;
    const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
    if (sizeInBytes > maxSizeInBytes) {
      return { success: false, error: 'Image too large (max 5MB)' };
    }

    const timestamp = Date.now();
    const filename = `exam_${examId}_${timestamp}.${fileExtension}`;

    const studentUploadDir = path.join(process.cwd(), 'uploads', 'exam-monitoring', studentId);
    if (!fs.existsSync(studentUploadDir)) {
      fs.mkdirSync(studentUploadDir, { recursive: true });
    }

    const filePath = path.join(studentUploadDir, filename);
    const buffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(filePath, buffer);

    const photoRecord = await prisma.examMonitoringPhoto.create({
      data: {
        studentId,
        examId,
        photoUrl: `/uploads/exam-monitoring/${studentId}/${filename}`,
        capturedAt: new Date(),
      },
    });

    return {
      success: true,
      data: {
        id: photoRecord.id,
        photoUrl: photoRecord.photoUrl,
        capturedAt: photoRecord.capturedAt,
      },
    };
  } catch (error: any) {
    console.error('Error saving exam photo:', error);
    return {
      success: false,
      error: error.message || 'Failed to save photo',
    };
  }
};

export const getStudentExamPhotos = async (
  studentId: string,
  examId: number
): Promise<PhotoUploadResult> => {
  try {
    const photos = await prisma.examMonitoringPhoto.findMany({
      where: {
        studentId,
        examId,
      },
      orderBy: {
        capturedAt: 'asc',
      },
      select: {
        id: true,
        photoUrl: true,
        capturedAt: true,
      },
    });

    return {
      success: true,
      data: photos,
    };
  } catch (error: any) {
    console.error('Error fetching exam photos:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch photos',
    };
  }
};

export const getAllExamPhotos = async (examId: number): Promise<PhotoUploadResult> => {
  try {
    const photos = await prisma.examMonitoringPhoto.findMany({
      where: {
        examId,
      },
      orderBy: {
        capturedAt: 'asc',
      },
      select: {
        id: true,
        studentId: true,
        photoUrl: true,
        capturedAt: true,
      },
    });

    return {
      success: true,
      data: photos,
    };
  } catch (error: any) {
    console.error('Error fetching all exam photos:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch photos',
    };
  }
};
