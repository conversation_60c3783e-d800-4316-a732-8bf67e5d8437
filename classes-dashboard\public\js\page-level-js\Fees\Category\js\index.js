/******/ (() => { // webpackBootstrap
/*!***********************************************************!*\
  !*** ./modules/Fees/resources/views/Category/js/index.js ***!
  \***********************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "Category Name",
  name: "Category Name"
}, {
  data: "Created At",
  name: "Created At"
}];
var table = commonDatatable("#category_table", categoryRoute.index, columns);
$(document).on("click", "#addCategoryEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = categoryRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Category");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteCategoryEntry", function () {
  var did = $(this).attr("data-deletecategoryid");
  var url = categoryRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;