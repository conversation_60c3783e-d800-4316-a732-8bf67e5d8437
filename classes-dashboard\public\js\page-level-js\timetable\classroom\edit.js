/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Classroom/resources/views/js/edit.js ***!
  \******************************************************/
$("#editclassroom_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    var url = editclassroomRoute.update;
    ajaxHandler(form, url, 'PATCH', '#editclassroom_form', '#saveclassroom', '#newClassroomEntry', '#classroom_table');
    return false;
  }
});
/******/ })()
;