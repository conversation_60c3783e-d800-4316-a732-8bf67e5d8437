/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Circulars/resources/views/js/index.js ***!
  \*******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "title",
  name: "title"
}, {
  data: "file",
  name: "file"
}, {
  data: "created_by",
  name: "created_by"
}];
var table = commonDatatable("#circular_table", circularsRoute.index, columns);
$(document).on("click", "#addCircularEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = circularsRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Circular");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteCircularEntry", function () {
  var circularid = $(this).attr("data-circularid");
  var url = circularsRoute["delete"];
  url = url.replace(":circularid", circularid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;