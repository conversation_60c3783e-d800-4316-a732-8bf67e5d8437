import prisma from "@/config/prismaClient";
import { Status } from "@prisma/client";
import { CreateStudentAdmissionInput } from "../requests/studentAdmissionRequest";
import bcrypt from "bcrypt";
import fs from "fs";
import path from "path";

export const createStudentAdmissionService = async (data: CreateStudentAdmissionInput & { photo?: string }) => {
  try {
    // Generate a default password for the student (can be changed later)
    const defaultPassword = `${data.first_name.toLowerCase()}${data.gr_no}`;
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    // Create the student first
    const student = await prisma.student.create({
      data: {
        firstName: data.first_name,
        lastName: data.last_name,
        email: data.email || `${data.gr_no}@student.uest.com`, // Generate email if not provided
        contact: data.contact_no,
        password: hashedPassword,
        isVerified: true, // Auto-verify students created through admission
      },
    });

    // Handle photo file movement if uploaded
    let finalPhotoPath: string | undefined;
    if (data.photo) {
      try {
        // Create student-specific directory
        const studentDir = path.join('uploads', 'students', student.id);
        fs.mkdirSync(studentDir, { recursive: true });
        
        // Move file from temp to student directory
        const fileName = `profile-photo${path.extname(data.photo)}`;
        const finalPath = path.join(studentDir, fileName);
        
        fs.copyFileSync(data.photo, finalPath);
        fs.unlinkSync(data.photo); // Remove temp file
        
        finalPhotoPath = finalPath;
      } catch (photoError) {
        console.warn('Failed to move photo file:', photoError);
        // Continue without photo if file operation fails
      }
    }

    // Create the student profile with student-specific data only
    const studentProfile = await prisma.studentProfile.create({
      data: {
        studentId: student.id,
        
        // Basic profile fields
        medium: data.department, // Using department as medium for now
        classroom: data.classroom,
        address: data.address,
        photo: finalPhotoPath,
        status: Status.PENDING,
        
        // Student personal details only
        family_name: data.family_name,
        first_name: data.first_name,
        middle_name: data.middle_name,
        last_name: data.last_name,
        gender: data.gender,
        date_of_birth: data.date_of_birth ? new Date(data.date_of_birth) : null,
        age: data.age ? parseInt(data.age) : null,
        aadhaar_no: data.aadhaar_no ? BigInt(data.aadhaar_no) : null,
        blood_group: data.blood_group,
        birth_place: data.birth_place,
        mother_tongue: data.mother_tongue,
        city: data.city,
        pin: data.pin ? BigInt(data.pin) : null,
        district: data.district,
        state: data.state,
        country: data.country,
        religion: data.religion,
        caste: data.caste,
        sub_caste: data.sub_caste,
        contact_no: data.contact_no ? BigInt(data.contact_no) : null,
        email: data.email,
      },
    });

    // Create separate parent details record
    const parentDetails = await prisma.parentDetails.create({
      data: {
        studentId: student.id,
        
        // Father's details
        fathers_name: data.fathers_name,
        fathers_middle_name: data.fathers_middle_name,
        fathers_last_name: data.fathers_last_name,
        fathers_qualification: data.fathers_qualification,
        fathers_occupation: data.fathers_occupation,
        fathers_aadhaar_no: data.fathers_aadhaar_no,
        
        // Mother's details
        mothers_name: data.mothers_name,
        mothers_middle_name: data.mothers_middle_name,
        mothers_last_name: data.mothers_last_name,
        mothers_qualification: data.mothers_qualification,
        mothers_occupation: data.mothers_occupation,
        mothers_aadhaar_no: data.mothers_aadhaar_no,
        
        // Contact and family details
        contact_no_1: data.contact_no_1,
        contact_no_2: data.contact_no_2,
        family_income: data.family_income,
        part_of_ngo: data.part_of_ngo,
      },
    });

    return {
      student,
      studentProfile,
      parentDetails,
      defaultPassword, // Return for notification purposes
    };
  } catch (error: any) {
    console.error("Error creating student admission:", error);
    throw new Error(`Failed to create student admission: ${error.message}`);
  }
};

export const getStudentAdmissionById = async (studentId: string) => {
  return await prisma.student.findUnique({
    where: { id: studentId },
    include: {
      profile: true,
      parentDetails: true,
    },
  });
};

export const getAllStudentAdmissions = async (options: {
  page?: number;
  limit?: number;
  status?: Status;
} = {}) => {
  const { page = 1, limit = 10, status } = options;
  const skip = (page - 1) * limit;

  const where = status ? { profile: { status } } : {};

  const [students, total] = await Promise.all([
    prisma.student.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
      include: {
        profile: true,
        parentDetails: true,
      },
    }),
    prisma.student.count({ where }),
  ]);

  return {
    students,
    total,
    totalPages: Math.ceil(total / limit),
    currentPage: page,
  };
};

export const updateStudentAdmissionStatus = async (studentId: string, status: Status) => {
  return await prisma.studentProfile.update({
    where: { studentId },
    data: { status },
  });
};
