import prisma from '@/config/prismaClient';
import { Status, NotificationType } from '@prisma/client';
import { getConstantsByCategory } from './constantService';
import { createAdminNotification } from '@/utils/notifications';
import bcrypt from 'bcrypt';


export const createStudentProfile = async (
  studentId: string,
  medium: string,
  classroom: string,
  birthday: Date,
  school: string,
  address: string,
  photo?: string,
  documentUrl?: string,
  status: Status = 'PENDING'
) => {
  return prisma.studentProfile.create({
    data: {
      studentId,
      medium,
      classroom,
      birthday,
      school,
      address,
      photo,
      documentUrl,
      status,
    },
  });
};


export const getStudentProfileByStudentId = async (studentId: string) => {
  return prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });
};

export const getStudentWithProfileAndOptions = async (studentId: string) => {
    // Get student profile with student data
  const profile = await prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });

  // Get student coins
  const coins = await prisma.uestCoins.findFirst({
    where: {
      modelType: 'STUDENT',
      modelId: studentId,
    },
  });

  // Get classroom options
  const classroomCategory = await getConstantsByCategory('classroom');
  const classroomOptions = classroomCategory?.details || [];

    // Combine all data
  return {
    profile,
    coins: coins?.coins ?? 0,
    classroomOptions,
  };
};

// Get student data with profile for admin
export const getStudentWithProfileForAdmin = async (studentId: string) => {
  // Get student profile with student data
  const profile = await prisma.studentProfile.findUnique({
    where: {
      studentId,
    },
    include: {
      student: true,
    },
  });

  // Get student coins
  const coins = await prisma.uestCoins.findFirst({
    where: {
      modelType: 'STUDENT',
      modelId: studentId,
    },
  });

  const classroomCategory = await getConstantsByCategory('classroom');
  const classroomOptions = classroomCategory?.details || [];

  // If profile exists, add coins to student data
  if (profile && profile.student) {
    (profile.student as any).coins = coins?.coins ?? 0;
  }

  return {
    profile,
    classroomOptions,
  };
};


export const updateStudentProfile = async (
  studentId: string,
  data: {
    medium?: string;
    classroom?: string;
    birthday?: Date;
    school?: string;
    address?: string;
    photo?: string;
    documentUrl?: string;
    status?: Status;
  }
) => {
  return prisma.studentProfile.update({
    where: {
      studentId,
    },
    data,
  });
};


export const updateStudentProfileStatus = async (
  studentId: string,
  status: Status
) => {
  return prisma.studentProfile.update({
    where: {
      studentId,
    },
    data: {
      status,
    },
  });
};


export const deleteStudentProfile = async (studentId: string) => {
  return prisma.studentProfile.delete({
    where: {
      studentId,
    },
  });
};

// Update both student and profile data in one transaction
export const updateStudentAndProfile = async (
  studentId: string,
  studentData: {
    firstName?: string;
    lastName?: string;
    contact?: string;
  },
  profileData: {
    medium?: string;
    classroom?: string;
    birthday?: Date;
    school?: string;
    address?: string;
    photo?: string;
    documentUrl?: string;
  }
) => {
  return prisma.$transaction(async (tx) => {
    // Update student data if provided
    if (Object.keys(studentData).length > 0) {
      await tx.student.update({
        where: { id: studentId },
        data: studentData,
      });
    }

    // Check if profile exists
    const existingProfile = await tx.studentProfile.findUnique({
      where: { studentId },
    });

    if (Object.keys(profileData).length > 0) {
      if (existingProfile) {
        await tx.studentProfile.update({
          where: { studentId },
          data: profileData,
        });
      } else {
        if (!profileData.medium || !profileData.classroom || !profileData.birthday || !profileData.school || !profileData.address) {
          throw new Error('Missing required profile fields for new profile creation');
        }

        await tx.studentProfile.create({
          data: {
            studentId,
            medium: profileData.medium,
            classroom: profileData.classroom,
            birthday: profileData.birthday,
            school: profileData.school,
            address: profileData.address,
            photo: profileData.photo,
            documentUrl: profileData.documentUrl,
            status: 'PENDING',
          },
        });
      }
    }

    // Return the updated data
    const updatedProfile = await tx.studentProfile.findUnique({
      where: { studentId },
      include: { student: true },
    });

    return updatedProfile;
  });
};

export const deleteStudentService = async (studentId: string) => {
  try {
    await prisma.$transaction(async (tx) => {
      await Promise.all([
        tx.studentProfile.deleteMany({ where: { studentId } }), 
        tx.studentWishlist.deleteMany({ where: { studentId } }),
        tx.classesReviews.deleteMany({ where: { studentId } }),
        tx.sMWaterParkTicket.deleteMany({ where: { studentId } }),
        tx.shivWaterParkTicket.deleteMany({ where: { studentId } }),
        tx.sMWaterParkAdminEntry.deleteMany({ where: { studentId } }),
        tx.shivWaterParkAdminEntry.deleteMany({ where: { studentId } }),
        tx.uestCoins.deleteMany({ where: { modelId: studentId, modelType: 'STUDENT' } }),
        tx.uestCoinTransaction.deleteMany({ where: { modelId: studentId, modelType: 'STUDENT' } }),
        tx.referralEarning.deleteMany({ where: { studentId } }),
      ]);

      await tx.student.delete({ where: { id: studentId } });
    });

  } catch (error: any) {
    console.error(`Error in deleteStudentService for studentId ${studentId}:`, {
      message: error.message,
      code: error.code,
      stack: error.stack,
    });
    throw error;
  }
};
export const createStudentAdmission = async (admissionData: any) => {
  try {
    const defaultPassword = `${admissionData.first_name.toLowerCase()}${admissionData.gr_no}`;
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    // Create the student first
    const student = await prisma.student.create({
      data: {
        firstName: admissionData.first_name,
        lastName: admissionData.last_name,
        email: admissionData.email || `${admissionData.gr_no}@student.uest.com`,
        contact: admissionData.contact_no,
        password: hashedPassword,
        isVerified: true,
      },
    });

    // Create the student profile
    const studentProfile = await prisma.studentProfile.create({
      data: {
        studentId: student.id,
        medium: admissionData.department,
        classroom: admissionData.classroom,
        address: admissionData.address,
        photo: admissionData.photo,
        status: Status.PENDING,

        // Student personal details
        family_name: admissionData.family_name,
        first_name: admissionData.first_name,
        middle_name: admissionData.middle_name,
        last_name: admissionData.last_name,
        gender: admissionData.gender,
        date_of_birth: admissionData.date_of_birth ? new Date(admissionData.date_of_birth) : null,
        age: admissionData.age ? parseInt(admissionData.age) : null,
        aadhaar_no: admissionData.aadhaar_no ? BigInt(admissionData.aadhaar_no) : null,
        blood_group: admissionData.blood_group,
        birth_place: admissionData.birth_place,
        mother_tongue: admissionData.mother_tongue,
        city: admissionData.city,
        pin: admissionData.pin ? BigInt(admissionData.pin) : null,
        district: admissionData.district,
        state: admissionData.state,
        country: admissionData.country,
        religion: admissionData.religion,
        caste: admissionData.caste,
        sub_caste: admissionData.sub_caste,
        contact_no: admissionData.contact_no ? BigInt(admissionData.contact_no) : null,
        email: admissionData.email,
      },
    });

    // Create separate parent details
    const parentDetails = await prisma.studentParentDetails.create({
      data: {
        studentId: student.id,
        fathers_name: admissionData.fathers_name,
        fathers_middle_name: admissionData.fathers_middle_name,
        fathers_last_name: admissionData.fathers_last_name,
        fathers_qualification: admissionData.fathers_qualification,
        fathers_occupation: admissionData.fathers_occupation,
        fathers_aadhaar_no: admissionData.fathers_aadhaar_no,
        mothers_name: admissionData.mothers_name,
        mothers_middle_name: admissionData.mothers_middle_name,
        mothers_last_name: admissionData.mothers_last_name,
        mothers_qualification: admissionData.mothers_qualification,
        mothers_occupation: admissionData.mothers_occupation,
        mothers_aadhaar_no: admissionData.mothers_aadhaar_no,
        contact_no_1: admissionData.contact_no_1,
        contact_no_2: admissionData.contact_no_2,
        family_income: admissionData.family_income,
        part_of_ngo: admissionData.part_of_ngo,
      },
    });

    return {
      student,
      studentProfile,
      parentDetails,
      defaultPassword,
    };
  } catch (error: any) {
    console.error("Error creating student admission:", error);
    throw new Error(`Failed to create student admission: ${error.message}`);
  }
};
