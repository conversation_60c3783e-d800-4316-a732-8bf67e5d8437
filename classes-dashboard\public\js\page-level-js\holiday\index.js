/******/ (() => { // webpackBootstrap
/*!*****************************************************!*\
  !*** ./modules/Holiday/resources/views/js/index.js ***!
  \*****************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "holiday_name",
  name: "holiday_name"
}, {
  data: "date",
  name: "date"
}];
var table = commonDatatable("#holiday_table", holidayRoute.index, columns);
$(document).on("click", "#addHolidayEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = holidayRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Holiday");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteHolidayEntry", function () {
  var did = $(this).attr("data-deleteholidayid");
  var url = holidayRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).ready(function () {
  function noSunday(date) {
    return [date.getDay() != 0, ""];
  }
  var year = new Date().getFullYear();
  $("body").delegate("#holiday_date", "focusin", function () {
    $(this).datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: false,
      minDate: new Date(startYearDate),
      maxDate: new Date(endYearDate),
      beforeShowDay: noSunday
    });
  });
});
$(document).on("click", ".editHolidayEntry", function () {
  var editid = $(this).attr("data-editholidayid");
  var url = holidayRoute.edit;
  url = url.replace(":editid", editid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Event");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".exportData", function () {
  var url = holidayRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;