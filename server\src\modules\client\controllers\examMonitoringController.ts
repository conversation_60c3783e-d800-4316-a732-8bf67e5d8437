import { Request, Response } from 'express';
import { saveExamPhoto, getStudentExamPhotos, getAllExamPhotos } from '../services/examMonitoringService';
import { sendSuccess, sendError } from '@/utils/response';

export const uploadExamPhoto = async (req: Request, res: Response): Promise<void> => {
  try {
    const { studentId, examId, photoData } = req.body;

    if (!studentId || !examId || !photoData) {
      sendError(res, 'Missing required fields: studentId, examId, photoData', 400);
      return;
    }

    const examIdNum = parseInt(examId);
    if (isNaN(examIdNum)) {
      sendError(res, 'Invalid examId. Must be a number.', 400);
      return;
    }

    if (!photoData.startsWith('data:image/')) {
      sendError(res, 'Invalid image format. Expected base64 image data.', 400);
      return;
    }

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(studentId)) {
      sendError(res, 'Invalid studentId format', 400);
      return;
    }

    const result = await saveExamPhoto(studentId, examIdNum, photoData);

    if (result.success) {
      sendSuccess(res, result.data, 'Photo uploaded successfully', 201);
    } else {
      sendError(res, result.error || 'Failed to upload photo', 400);
    }
  } catch (error: any) {
    console.error('Error uploading exam photo:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const getExamPhotos = async (req: Request, res: Response): Promise<void> => {
  try {
    const { studentId, examId } = req.params;

    if (!studentId || !examId) {
      sendError(res, 'Missing required parameters: studentId, examId', 400);
      return;
    }

    const result = await getStudentExamPhotos(studentId, parseInt(examId));

    if (result.success) {
      sendSuccess(res, result.data, 'Photos retrieved successfully');
    } else {
      sendError(res, result.error || 'Failed to fetch photos', 500);
    }
  } catch (error: any) {
    console.error('Error fetching exam photos:', error);
    sendError(res, 'Internal server error', 500);
  }
};

export const getAllExamPhotosController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { examId } = req.params;

    if (!examId) {
      sendError(res, 'Missing required parameter: examId', 400);
      return;
    }

    const result = await getAllExamPhotos(parseInt(examId));

    if (result.success) {
      sendSuccess(res, result.data, 'All exam photos retrieved successfully');
    } else {
      sendError(res, result.error || 'Failed to fetch photos', 500);
    }
  } catch (error: any) {
    console.error('Error fetching all exam photos:', error);
    sendError(res, 'Internal server error', 500);
  }
};
