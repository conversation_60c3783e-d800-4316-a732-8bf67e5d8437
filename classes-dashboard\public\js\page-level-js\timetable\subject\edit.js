/******/ (() => { // webpackBootstrap
/*!****************************************************!*\
  !*** ./modules/Subject/resources/views/js/edit.js ***!
  \****************************************************/
$("#editsubject_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    var url = editsubjectRoute.update;
    ajaxHandler(form, url, 'PATCH', '#editsubject_form', '#savesubject', '#newSubjectEntry', '#subject_table');
    return false;
  }
});
/******/ })()
;