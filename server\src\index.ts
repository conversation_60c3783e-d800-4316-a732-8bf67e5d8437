import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import path from "path";
import router from "./router";
import cookieParser from "cookie-parser";
import http from 'http';
import { io } from './socket/socket';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 4005;
const server = http.createServer(app);
app.use(
  helmet({
    crossOriginResourcePolicy: {
      policy: "same-site",
    },
  })
);
io.attach(server);

const allowedOrigins = [
  process.env.FRONTEND_URL,
  process.env.ADMIN_URL,
  process.env.DASHBOARD_URL,
  "http://localhost:3000",
  "http://localhost:3001",
  "http://localhost:8000",
  "http://127.0.0.1:8000",
  "*"
];

// Temporary permissive CORS for development
app.use(
  cors({
    origin: true, // Allow all origins in development
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    credentials: true,
    optionsSuccessStatus: 200,
  })
);

app.use(cookieParser());
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ limit: '5mb', extended: true }));

const limiter = rateLimit({
  windowMs: 1 * 60 * 1000,
  limit: 500,
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

app.disable("x-powered-by");

app.use("/uploads", express.static(path.join(__dirname, "..", "uploads")));

app.use("/api/v1", router);

app.use((err: any, res: express.Response) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

server.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`);
});

export = app;