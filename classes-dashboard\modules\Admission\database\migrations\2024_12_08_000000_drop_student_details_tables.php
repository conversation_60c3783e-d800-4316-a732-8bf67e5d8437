<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropStudentDetailsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('student_siblings_details');
        Schema::dropIfExists('student_health_details');
        Schema::dropIfExists('student_past_details');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Recreate student_siblings_details table
        Schema::create('student_siblings_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->foreign('student_id')->references('id')->on('student_details')->onDelete('cascade');
            $table->string('sibling_name')->nullable();
            $table->date('sibling_date_of_birth')->nullable();
            $table->string('studying_std')->nullable();
            $table->string('school_name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Recreate student_health_details table
        Schema::create('student_health_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->foreign('student_id')->references('id')->on('student_details')->onDelete('cascade');
            $table->string('eye_sight')->nullable();
            $table->string('hear_ability')->nullable();
            $table->string('allergy_1')->nullable();
            $table->string('allergy_2')->nullable();
            $table->string('any_health_issue')->nullable();
            $table->string('doctors_name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Recreate student_past_details table
        Schema::create('student_past_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('student_id')->nullable();
            $table->foreign('student_id')->references('id')->on('student_details')->onDelete('cascade');
            $table->string('prev_standard')->nullable();
            $table->string('prev_school')->nullable();
            $table->string('prev_passing_year')->nullable();
            $table->date('prev_school_left_date')->nullable();
            $table->string('left_reason')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }
}
