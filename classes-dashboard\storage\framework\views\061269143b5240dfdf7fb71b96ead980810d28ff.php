<div id="student-details" class="content" role="tabpanel" aria-labelledby="student-details-trigger">
    <div id="student-details-forms">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="student_id" />
        <input type="hidden" name="year" value="<?php echo e(getActiveYearId()); ?>" />
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Department *</label>
                    <select id="department" class="form-control select2" name="department">
                    <option value="">Select department</option>
                    <?php $__currentLoopData = $department; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value->id); ?>"><?php echo e($value->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Classroom *</label>
                    <select id="classroom" class="form-control select2 classroom-data" name="classroom">
                    <option value="">Select Classroom</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('gr_no', 'GR No (UID Number) *',['class' => 'form-label']); ?>

                    <?php echo Form::text('gr_no', null, ['id' => 'gr_no','placeholder'=>'Enter GR No','class' => 'form-control']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('family_name', 'Family(Pet) Name',['class' => 'form-label']); ?>

                    <?php echo Form::text('family_name', null, ['id' => 'family_name','placeholder'=>'Enter Family Name','class' => 'form-control firstcap']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('first_name', 'First Name *',['class' => 'form-label']); ?>

                    <?php echo Form::text('first_name', null, ['id' => 'first_name','placeholder'=>'Enter First Name','class' => 'form-control firstcap']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('middle_name', 'Middle Name *',['class' => 'form-label']); ?>

                    <?php echo Form::text('middle_name', null, ['id' => 'middle_name','placeholder'=>'Enter Middle','class' => 'form-control firstcap']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('last_name', 'Last Name *',['class' => 'form-label']); ?>

                    <?php echo Form::text('last_name', null, ['id' => 'last_name','placeholder'=>'Enter Last Name','class' => 'form-control firstcap']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="gender">Gender *</label>
                    <div class="custom-control custom-radio <?php echo e($errors->has('date_of_birth') ? 'is-invalid':''); ?>">
                        <input class="custom-control-input" type="radio" value="Male" <?php echo e(isset($data->gender) && $data->gender == 'Male' ? 'checked' : ''); ?> id="male" name="gender">
                        <label for="male" class="custom-control-label">Male</label>
                    </div>
                    <div class="custom-control custom-radio <?php echo e($errors->has('date_of_birth') ? 'is-invalid':''); ?>">
                        <input class="custom-control-input" type="radio" value="Female" <?php echo e(isset($data->gender) && $data->gender == 'Female' ? 'checked' : ''); ?> id="female" name="gender">
                        <label for="female" class="custom-control-label">Female</label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('contact_no', 'Contact Number *',['class' => 'form-label']); ?>

                    <?php echo Form::text('contact_no', null, ['class' => 'form-control ','placeholder'=>'Enter Contact Number']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('email', 'Email',['class' => 'form-label']); ?>

                    <?php echo Form::text('email', null, ['id' => 'email','placeholder'=>'Enter Email','class' => 'form-control']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('age', 'Age',['class' => 'form-label']); ?>

                    <?php echo Form::text('age', null, ['class' => 'form-control','placeholder'=>'Enter Age']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('date_of_birth', 'Date Of Birth',['class' => 'form-label']); ?>

                    <?php echo Form::text('date_of_birth', null, ['readonly','class' => 'form-control datepicker ','placeholder'=>'Enter Date Of Birth']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('aadhaar_no', 'Aadhaar Number',['class' => 'form-label']); ?>

                    <?php echo Form::text('aadhaar_no', null, ['class' => 'form-control ','placeholder'=>'Enter Aadhaar Number']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('blood_group', 'Blood Group',['class' => 'form-label']); ?>

                    <select id="blood_group" class="select2" name="blood_group">
                        <option value="">Select Blood Group</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('birth_place', 'Birth Place',['class' => 'form-label']); ?>

                    <?php echo Form::text('birth_place', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Birth Place']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('mother_tongue', 'Mother Tongue',['class' => 'form-label']); ?>

                    <?php echo Form::text('mother_tongue', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Mother Tongue']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('address', 'Address',['class' => 'form-label']); ?>

                    <?php echo Form::text('address', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Address']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('city', 'City',['class' => 'form-label']); ?>

                    <?php echo Form::text('city', null, ['class' => 'form-control firstcap','placeholder'=>'Enter City']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('pin', 'Pin',['class' => 'form-label']); ?>

                    <?php echo Form::text('pin', null, ['class' => 'form-control ','placeholder'=>'Enter Pin']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('district', 'District',['class' => 'form-label']); ?>

                    <?php echo Form::text('district', null, ['class' => 'form-control firstcap','placeholder'=>'Enter District']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('state', 'State',['class' => 'form-label']); ?>

                    <?php echo Form::text('state', null, ['class' => 'form-control firstcap','placeholder'=>'Enter State']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('country', 'Country',['class' => 'form-label']); ?>

                    <?php echo Form::text('country', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Country']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('religion', 'Religion',['class' => 'form-label']); ?>

                    <?php echo Form::text('religion', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Religion']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('caste', 'Caste',['class' => 'form-label']); ?>

                    <?php echo Form::text('caste', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Caste']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('sub_caste', 'Sub Caste',['class' => 'form-label']); ?>

                    <?php echo Form::text('sub_caste', null, ['class' => 'form-control firstcap','placeholder'=>'Enter Sub Caste']); ?>

                </div>
            </div>
            <div class="form-group ast-required col-sm-12">
                <label>Student Image (Only jpg,jpeg,png Allowed)</label>
                <div class="imageWrapper ">
                    <img class="image-product-logo" style="height:200px;width:200px" src="http://dummyimage.com/400x200/f5f5f5/000000&text=Student+Photo">
                    <button class="file-upload">
                        <input type="file" name="photo" class="file-input form-control">Choose Photo
                    </button>
                </div>
            </div>
        </div>
    </div>
    <button class="btn btn-primary" id="submit-student-details">Save & Next</button>
    <button class="btn btn-secondary firststep d-none" onclick="stepper.next()">Skip</button>
</div><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/steps/student-details.blade.php ENDPATH**/ ?>