import { axiosInstance } from '@/lib/axios';

export interface PhotoUploadData {
  studentId: string;
  examId: number;
  photoData: string; // base64 image data
}

export interface PhotoUploadResponse {
  success: boolean;
  data?: {
    id: string;
    photoUrl: string;
    capturedAt: string;
  };
  error?: string;
}

export const uploadExamPhoto = async (data: PhotoUploadData): Promise<PhotoUploadResponse> => {
  try {
    const response = await axiosInstance.post('/exam-monitoring/upload-photo', data);
    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    console.error('Error uploading exam photo:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to upload photo',
    };
  }
};

