/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Timeslots/resources/views/js/index.js ***!
  \*******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "start_time",
  name: "start_time"
}, {
  data: "end_time",
  name: "end_time"
}, {
  data: "is_break",
  name: "is_break"
}];
var table = commonDatatable("#timeslots_table", timeslotsRoute.index, columns);
$(document).ready(function () {
  $("body").delegate("#start_time", "focusin", function () {
    $(this).datetimepicker({
      timeFormat: "h:mm p"
    });
  });
  $("body").delegate("#end_time", "focusin", function () {
    $(this).datetimepicker({
      timeFormat: "h:mm p"
    });
  });
});
$(document).on("click", "#addTimeslotsEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = timeslotsRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Timeslots");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".edittimeslotsEntry", function () {
  var editdid = $(this).attr("data-edittimeslotsid");
  var url = timeslotsRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Timeslots");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteTimeslotsEntry", function () {
  var did = $(this).attr("data-deletetimeslotsid");
  var url = timeslotsRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on("click", ".exportData", function () {
  var url = timeslotsRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;