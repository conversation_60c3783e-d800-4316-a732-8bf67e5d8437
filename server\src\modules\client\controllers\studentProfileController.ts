import { Request, Response } from 'express';
import { Status } from '@prisma/client';
import prisma from '@/config/prismaClient';
import bcrypt from 'bcrypt';
import {
  createStudentProfile,
  getStudentProfileByStudentId,
  updateStudentProfile,
  deleteStudentProfile,
  updateStudentProfileStatus,
  getStudentWithProfileAndOptions,
  updateStudentAndProfile,
  getStudentWithProfileForAdmin,
  deleteStudentService,
  createStudentAdmission,
} from '../services/studentProfileService';
import { sendError, sendSuccess } from '@/utils/response';
import { getConstantsByCategory } from '../services/constantService';
import { saveBase64ToFile, validateBase64FileSize, validateMimeType, cleanupOldFiles } from '@/utils/fileUtils';
import { createNotification, createAdminNotification } from '@/utils/notifications';
import { UserType, NotificationType } from '@prisma/client';

// Type for authenticated requests with student data
type AuthenticatedRequest = Request & {
  student?: {
    id: string;
  };
};


export const createStudentProfileController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (existingProfile) {
      return sendError(res, 'Profile already exists', 400);
    }

    const { medium, classroom, birthday, school, address } = req.body;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] } | undefined;

    // Create profile
    const profile = await createStudentProfile(
      studentId,
      medium,
      classroom,
      new Date(birthday),
      school,
      address,
      files?.photo?.[0]?.path,
      files?.document?.[0]?.path
    );

    // Get student details for notification
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Create notification for admin about new profile submission
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'New Student Profile Submitted',
      message: `Student ${student?.firstName} ${student?.lastName} has submitted their profile for review.`,
      data: {
        studentId: studentId,
        studentName: `${student?.firstName} ${student?.lastName}`,
        email: student?.email,
        profileId: profile.id
      }
    });

    return sendSuccess(res, profile, 'Profile created successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to create profile', 500);
  }
};


export const getStudentProfileController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const profile = await getStudentProfileByStudentId(studentId);
    if (!profile) {
      return sendError(res, 'Profile not found', 404);
    }

    return sendSuccess(res, profile, 'Profile retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to retrieve profile', 500);
  }
};

// Get all student data in one call (profile, student, coins, classroom options)
export const getStudentAllDataController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const allData = await getStudentWithProfileAndOptions(studentId);
    return sendSuccess(res, allData, 'Student data retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get student data', 500);
  }
};


export const updateStudentProfileController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (!existingProfile) {
      return sendError(res, 'Profile not found', 404);
    }

    const { medium, classroom, birthday, school, address } = req.body;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] } | undefined;

    // Prepare update data
    const updateData: Record<string, any> = {};
    if (medium) updateData.medium = medium;
    if (classroom) updateData.classroom = classroom;
    if (birthday) updateData.birthday = new Date(birthday);
    if (school) updateData.school = school;
    if (address) updateData.address = address;
    if (files?.photo?.[0]?.path) updateData.photo = files.photo[0].path;
    if (files?.document?.[0]?.path) updateData.documentUrl = files.document[0].path;

    if (Object.keys(updateData).length === 0) {
      return sendError(res, 'No data provided for update', 400);
    }

    const updatedProfile = await updateStudentProfile(studentId, updateData);

    // Get student details for notification
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Create notification for admin about profile update
    await createAdminNotification({
      type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
      title: 'Student Profile Updated',
      message: `Student ${student?.firstName} ${student?.lastName} has updated their profile. Please review the changes.`,
      data: {
        studentId: studentId,
        studentName: `${student?.firstName} ${student?.lastName}`,
        email: student?.email,
        profileId: updatedProfile.id,
        updatedFields: Object.keys(updateData)
      }
    });

    return sendSuccess(res, updatedProfile, 'Profile updated successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to update profile', 500);
  }
};

// Update both student and profile data in one call
export const updateStudentAndProfileController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.params?.studentId || req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    // Extract data from request
    const {
      firstName, lastName, contact, medium, classroom, birthday, school, address,
      photo, photoMimeType, document, documentMimeType, documentName, removeDocument
    } = req.body;

    // Prepare data objects
    const studentData: Record<string, any> = {};
    const profileData: Record<string, any> = {};

    // Student data
    if (firstName) studentData.firstName = firstName;
    if (lastName) studentData.lastName = lastName;
    if (contact) studentData.contact = contact;

    // Profile data
    if (medium) profileData.medium = medium;
    if (classroom) profileData.classroom = classroom;
    if (birthday) profileData.birthday = new Date(birthday);
    if (school) profileData.school = school;
    if (address) profileData.address = address;

    // Get existing profile to check for old files that need cleanup
    const currentProfile = await getStudentProfileByStudentId(studentId);

    // Process Base64 photo if provided
    if (photo && photoMimeType) {
      // Validate photo
      if (!validateMimeType(photoMimeType, 'photo')) {
        return sendError(res, 'Invalid photo format. Only JPEG and PNG are allowed.', 400);
      }

      if (!validateBase64FileSize(photo, 5120, 0)) {
        return sendError(res, 'Photo size must not exceed 5MB', 400);
      }

      try {
        // Clean up old photo if it exists
        if (currentProfile?.photo) {
          cleanupOldFiles(currentProfile.photo, undefined);
        }

        const savedPhoto = await saveBase64ToFile(photo, photoMimeType, studentId, 'photo');
        profileData.photo = savedPhoto.filePath;
      } catch (error) {
        console.error('Photo save error:', error);
        return sendError(res, 'Failed to save photo', 500);
      }
    }

    // Process Base64 document if provided
    if (document && documentMimeType) {
      // Validate document
      if (!validateMimeType(documentMimeType, 'document')) {
        return sendError(res, 'Invalid document format. Only PDF, JPEG, and PNG are allowed.', 400);
      }

      if (!validateBase64FileSize(document, 5120, 0)) {
        return sendError(res, 'Document size must not exceed 5MB', 400);
      }

      try {
        // Clean up old document if it exists
        if (currentProfile?.documentUrl) {
          cleanupOldFiles(undefined, currentProfile.documentUrl);
        }

        const savedDocument = await saveBase64ToFile(document, documentMimeType, studentId, 'document', documentName);
        profileData.documentUrl = savedDocument.filePath;
      } catch (error) {
        console.error('Document save error:', error);
        return sendError(res, 'Failed to save document', 500);
      }
    }

    // Handle document removal
    if (removeDocument && currentProfile?.documentUrl) {
      try {
        cleanupOldFiles(undefined, currentProfile.documentUrl);
        profileData.documentUrl = null;
      } catch (error) {
        console.error('Document removal error:', error);
        return sendError(res, 'Failed to remove document', 500);
      }
    }

    // Validate request has data
    if (Object.keys(studentData).length === 0 && Object.keys(profileData).length === 0) {
      return sendError(res, 'No data provided for update', 400);
    }

    // Check if creating new profile
    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (!existingProfile && Object.keys(profileData).length > 0) {
      if (!medium || !classroom || !birthday || !school || !address) {
        return sendError(res, 'Missing required profile fields', 400);
      }
    }

    // Update data
    const updatedData = await updateStudentAndProfile(studentId, studentData, profileData);

    // Get updated student details for notification
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: { firstName: true, lastName: true, email: true }
    });

    // Create notification for admin about combined profile update
    if (Object.keys(profileData).length > 0) {
      await createAdminNotification({
        type: NotificationType.ADMIN_PROFILE_REVIEW_REQUIRED,
        title: 'Student Profile & Data Updated',
        message: `Student ${student?.firstName} ${student?.lastName} has updated their profile and personal information. Please review the changes.`,
        data: {
          studentId: studentId,
          studentName: `${student?.firstName} ${student?.lastName}`,
          email: student?.email,
          updatedStudentFields: Object.keys(studentData),
          updatedProfileFields: Object.keys(profileData)
        }
      });
    }

    return sendSuccess(res, updatedData, 'Student and profile updated successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to update student and profile', 500);
  }
};


export const deleteStudentProfileController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (!existingProfile) {
      return sendError(res, 'Profile not found', 404);
    }

    await deleteStudentProfile(studentId);
    return sendSuccess(res, null, 'Profile deleted successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to delete profile', 500);
  }
};

export const getClassroomOptionsController = async (_req: Request, res: Response): Promise<any> => {
  try {
    const constants = await getConstantsByCategory('Education');
    if (!constants) {
      return sendError(res, 'Classroom options not found', 404);
    }

    return sendSuccess(res, constants, 'Classroom options retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to retrieve classroom options', 500);
  }
};

export const updateStudentProfileStatusController = async (req: AuthenticatedRequest, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (!existingProfile) {
      return sendError(res, 'Profile not found', 404);
    }

    const { status } = req.body;
    const updatedProfile = await updateStudentProfileStatus(studentId, status as Status);
    return sendSuccess(res, updatedProfile, 'Profile status updated successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to update profile status', 500);
  }
};

export const getStudentProfileByIdController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId } = req.params;
    if (!studentId) {
      return sendError(res, 'Student ID is required', 400);
    }

    const profile = await getStudentProfileByStudentId(studentId);
    if (!profile) {
      return sendError(res, 'Student profile not found', 404);
    }

    return sendSuccess(res, profile, 'Student profile retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to retrieve student profile', 500);
  }
};

// Get all student data in one call for admin
export const getStudentAllDataForAdminController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId } = req.params;
    if (!studentId) {
      return sendError(res, 'Student ID is required', 400);
    }

    const allData = await getStudentWithProfileForAdmin(studentId);
    return sendSuccess(res, allData, 'Student data retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get student data', 500);
  }
};

export const updateStudentProfileStatusByAdminController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId } = req.params;
    if (!studentId) {
      return sendError(res, 'Student ID is required', 400);
    }

    const { status } = req.body;
    const existingProfile = await getStudentProfileByStudentId(studentId);
    if (!existingProfile) {
      return sendError(res, 'Student profile not found', 404);
    }

    const updatedProfile = await updateStudentProfileStatus(studentId, status as Status);

    // Create notification for student
    const notificationType = status === 'APPROVED' ? NotificationType.STUDENT_PROFILE_APPROVED : NotificationType.STUDENT_PROFILE_REJECTED;
    const title = status === 'APPROVED' ? 'Profile Approved!' : 'Profile Rejected';
    const message = status === 'APPROVED'
      ? 'Your profile has been approved by admin. You can now access all features.'
      : 'Your profile has been rejected by admin. Please update your profile and resubmit.';

    await createNotification({
      userId: studentId,
      userType: UserType.STUDENT,
      type: notificationType,
      title,
      message,
      data: { profileId: existingProfile.id, status }
    });

    return sendSuccess(res, updatedProfile, `Student profile status updated to ${status}`);
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to update student profile status', 500);
  }
};

export const deleteStudentController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId } = req.params; // Ensure studentId is extracted from params
    if (!studentId) {
      return sendError(res, 'Student ID is required', 400);
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
    });
    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    await deleteStudentService(studentId);
    return sendSuccess(res, null, 'Student deleted successfully');
  } catch (error: any) {
    console.error(`Error deleting student ${req.params.studentId}:`, error); // Use req.params.studentId directly
    if (error.message.includes('APPROVED')) {
      return sendError(res, error.message, 400);
    }
    if (error.code === 'P2003') {
      return sendError(res, 'Cannot delete student due to related records in other tables', 400);
    }
    return sendError(res, error.message || 'Failed to delete student', 500);
  }
};
export const saveParentDetailsController = async (req: Request, res: Response): Promise<any> => {
  try {
    console.log('Parent details request received');
    console.log('Request body:', req.body);
    console.log('Request files:', req.files);

    const { student_id, ...parentData } = req.body;

    if (!student_id) {
      return sendError(res, 'Student ID is required', 400);
    }
    const student = await prisma.student.findUnique({
      where: { id: student_id }
    });

    if (!student) {
      return sendError(res, 'Student not found', 404);
    }

    const parentDetails = await prisma.studentParentDetails.upsert({
      where: { studentId: student_id },
      update: {
        fathers_name: parentData.fathers_name,
        fathers_middle_name: parentData.fathers_middle_name,
        fathers_last_name: parentData.fathers_last_name,
        fathers_qualification: parentData.fathers_qualification,
        fathers_occupation: parentData.fathers_occupation,
        fathers_aadhaar_no: parentData.fathers_aadhaar_no,
        mothers_name: parentData.mothers_name,
        mothers_middle_name: parentData.mothers_middle_name,
        mothers_last_name: parentData.mothers_last_name,
        mothers_qualification: parentData.mothers_qualification,
        mothers_occupation: parentData.mothers_occupation,
        mothers_aadhaar_no: parentData.mothers_aadhaar_no,
        contact_no_1: parentData.contact_no_1,
        contact_no_2: parentData.contact_no_2,
        family_income: parentData.family_income,
        part_of_ngo: parentData.part_of_ngo,
      },
      create: {
        studentId: student_id,
        fathers_name: parentData.fathers_name,
        fathers_middle_name: parentData.fathers_middle_name,
        fathers_last_name: parentData.fathers_last_name,
        fathers_qualification: parentData.fathers_qualification,
        fathers_occupation: parentData.fathers_occupation,
        fathers_aadhaar_no: parentData.fathers_aadhaar_no,
        mothers_name: parentData.mothers_name,
        mothers_middle_name: parentData.mothers_middle_name,
        mothers_last_name: parentData.mothers_last_name,
        mothers_qualification: parentData.mothers_qualification,
        mothers_occupation: parentData.mothers_occupation,
        mothers_aadhaar_no: parentData.mothers_aadhaar_no,
        contact_no_1: parentData.contact_no_1,
        contact_no_2: parentData.contact_no_2,
        family_income: parentData.family_income,
        part_of_ngo: parentData.part_of_ngo,
      },
    });

    res.status(200).json({
      success: true,
      message: "Parent details saved successfully",
      data: {
        parentDetails,
      },
    });
  } catch (error: any) {
    console.error("Error in saveParentDetailsController:", error);
    return sendError(res, error.message || "Failed to save parent details", 500);
  }
};
export const createStudentStepOneController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentData = req.body;
    const photo = req.file?.path;

    const defaultPassword = `${studentData.first_name.toLowerCase()}${studentData.gr_no}`;
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const student = await prisma.student.create({
      data: {
        firstName: studentData.first_name,
        lastName: studentData.last_name,
        email: studentData.email,
        contact: studentData.contact_no,
        password: hashedPassword,
        isVerified: true,
      },
    });

    const studentProfile = await prisma.studentProfile.create({
      data: {
        studentId: student.id,
        classroom: studentData.classroom,
        family_name: studentData.family_name,
        first_name: studentData.first_name,
        middle_name: studentData.middle_name,
        last_name: studentData.last_name,
        gender: studentData.gender,
        age: studentData.age ? parseInt(studentData.age) : null,
        date_of_birth: studentData.date_of_birth ? new Date(studentData.date_of_birth) : null,
        aadhaar_no: studentData.aadhaar_no ? BigInt(studentData.aadhaar_no) : null,
        blood_group: studentData.blood_group,
        birth_place: studentData.birth_place,
        mother_tongue: studentData.mother_tongue,
        address: studentData.address,
        city: studentData.city,
        pin: studentData.pin ? BigInt(studentData.pin) : null,
        district: studentData.district,
        state: studentData.state,
        country: studentData.country,
        religion: studentData.religion,
        caste: studentData.caste,
        sub_caste: studentData.sub_caste,
        photo: photo,
        contact_no: studentData.contact_no ? BigInt(studentData.contact_no) : null,
        email: studentData.email,
      },
    });

    res.status(201).json({
      success: true,
      message: "Student details saved successfully. Please fill parent details.",
      data: {
        student: {
          id: student.id,
          firstName: student.firstName,
          lastName: student.lastName,
          email: student.email,
          contact: student.contact,
        },
        profile: {
          id: studentProfile.id,
          first_name: studentProfile.first_name,
          last_name: studentProfile.last_name,
          gender: studentProfile.gender,
          date_of_birth: studentProfile.date_of_birth,
        },
      },
    });
  } catch (error: any) {
    console.error("Error in createStudentStepOneController:", error);
    return sendError(res, error.message || "Failed to save student details", 500);
  }
};
export const createStudentAdmissionController = async (req: Request, res: Response): Promise<any> => {
  try {
    console.log('Student admission request received');
    console.log('Request body:', req.body);
    console.log('Request file:', req.file);

    const admissionData = req.body;
    const photo = req.file?.path;
    const result = await createStudentAdmission({
      ...admissionData,
      photo,
    });

    res.status(201).json({
      success: true,
      message: "Student admission created successfully",
      data: {
        student: result.student,
        profile: result.studentProfile,
        parentDetails: result.parentDetails,
      },
    });
  } catch (error: any) {
    console.error("Error in createStudentAdmissionController:", error);
    return sendError(res, error.message || "Failed to create student admission", 500);
  }
};