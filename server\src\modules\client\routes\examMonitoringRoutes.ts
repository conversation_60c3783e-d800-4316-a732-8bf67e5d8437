import { Router } from 'express';
import { uploadExamPhoto, getExamPhotos, getAllExamPhotosController } from '../controllers/examMonitoringController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const examMonitoringRouter = Router();

examMonitoringRouter.post('/upload-photo', studentAuthMiddleware, uploadExamPhoto);

examMonitoringRouter.get('/photos/:studentId/:examId', authMiddleware, getExamPhotos);

examMonitoringRouter.get('/photos/exam/:examId', authMiddleware, getAllExamPhotosController);

export default examMonitoringRouter;
