"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Camera, CameraOff, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { uploadExamPhoto } from '@/services/examMonitoringApi';

interface ExamCameraMonitoringProps {
  studentId: string;
  examId: number;
  isExamActive: boolean;
  onCameraError?: (error: string) => void;
  onCameraStatus?: (isActive: boolean) => void;
}

const ExamCameraMonitoring: React.FC<ExamCameraMonitoringProps> = ({
  studentId,
  examId,
  isExamActive,
  onCameraError,
  onCameraStatus,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [isCameraActive, setIsCameraActive] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);

  // Start camera
  const startCamera = useCallback(async () => {
    try {
      setCameraError(null);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: 640, height: 480, facingMode: 'user' }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;
        setIsCameraActive(true);
        onCameraStatus?.(true);
      }
    } catch (error: any) {
      const errorMessage = error.name === 'NotAllowedError'
        ? 'Camera access denied'
        : 'Camera not available';
      setCameraError(errorMessage);
      onCameraError?.(errorMessage);
      onCameraStatus?.(false);
      toast.error(errorMessage);
    }
  }, [onCameraError , onCameraStatus]);

  // Take photo and upload
  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current || !isCameraActive) return;

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      if (!context) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      const photoData = canvas.toDataURL('image/jpeg', 0.7);
      const result = await uploadExamPhoto({ studentId, examId, photoData });

      if (!result.success) {
        console.error('Failed to upload photo:', result.error);
      }
    } catch (error) {
      console.error('Failed to capture or upload photo:', error);
    }
  }, [studentId, examId, isCameraActive]);

  // Stop camera and photos
  const stopCamera = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsCameraActive(false);
  }, []);

  useEffect(() => {
    if (isExamActive && !isCameraActive) {
      startCamera();
    } else if (!isExamActive && isCameraActive) {
      stopCamera();
    }
  }, [isExamActive, isCameraActive, startCamera, stopCamera]);

  useEffect(() => {
    if (isCameraActive && isExamActive) {
      const firstPhotoTimeout = setTimeout(capturePhoto, 10000);
      intervalRef.current = setInterval(capturePhoto, 120000);

      return () => {
        clearTimeout(firstPhotoTimeout);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isCameraActive, isExamActive, capturePhoto]);

  useEffect(() => {
    return () => stopCamera();
  }, [stopCamera]);

  return (
    <div className="fixed top-4 right-4 z-50">
      <div className="bg-black rounded-lg p-2 shadow-lg border-2 border-orange-500">
        <div className="flex items-center gap-2 mb-2">
          {isCameraActive ? (
            <Camera className="w-4 h-4 text-green-500" />
          ) : (
            <CameraOff className="w-4 h-4 text-red-500" />
          )}
          <span className="text-white text-xs font-medium">
            {isCameraActive ? 'Monitoring Active' : 'Camera Inactive'}
          </span>
        </div>

        {cameraError ? (
          <div className="w-32 h-24 bg-red-900 rounded flex items-center justify-center">
            <AlertTriangle className="w-6 h-6 text-red-400" />
          </div>
        ) : (
          <video
            ref={videoRef}
            className="w-32 h-24 rounded object-cover transform scale-x-[-1]"
            autoPlay
            playsInline
            muted
          />
        )}

        {cameraError && (
          <div className="mt-2 text-xs text-red-400 max-w-32">
            {cameraError}
          </div>
        )}
      </div>

      {/* Hidden canvas for photo capture */}
      <canvas
        ref={canvasRef}
        className="hidden"
      />
    </div>
  );
};

export default ExamCameraMonitoring;
