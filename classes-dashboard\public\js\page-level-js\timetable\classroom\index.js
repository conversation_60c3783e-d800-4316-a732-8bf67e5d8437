/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Classroom/resources/views/js/index.js ***!
  \*******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "class_name",
  name: "class_name"
}, {
  data: "department_name",
  name: "department.name"
}];
var table = commonDatatable("#classroom_table", classroomRoute.index, columns);
$(document).on("click", "#addClassroomEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = classroomRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Classroom");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editclassroomEntry", function () {
  var editdid = $(this).attr("data-editclassroomid");
  var url = classroomRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Classroom");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteClassroomEntry", function () {
  commonAlert(function () {
    commonAjax($.extend({}, doAjax_params_default, {
      url: classroomRoute["delete"].replace(":did", $(this).attr("data-deleteclassroomid")),
      requestType: "DELETE",
      successCallbackFunction: function successCallbackFunction(result) {
        toastr.success(result.success);
        table.draw();
      }
    }));
  }.bind(this));
});
$(document).on("click", ".exportData", function () {
  var url = classroomRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;