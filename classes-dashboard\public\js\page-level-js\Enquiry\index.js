/******/ (() => { // webpackBootstrap
/*!*****************************************************!*\
  !*** ./modules/Enquiry/resources/views/js/index.js ***!
  \*****************************************************/
var columns = [{
  data: 'action',
  name: 'action',
  orderable: false
}, {
  data: 'transfer_to',
  name: 'transfer_to',
  orderable: false
}, {
  data: 'enquiry.created_at',
  name: 'enquiry.created_at'
}, {
  data: 'student_full_name',
  name: 'student_full_name'
}, {
  data: 'contact_number',
  name: 'contact_number',
  defaultContent: 'N/A'
}, {
  data: 'email',
  name: 'email',
  defaultContent: 'N/A'
}, {
  data: 'department',
  name: 'enquiry.department'
}, {
  data: 'classroom',
  name: 'enquiry.classroom'
}, {
  data: 'comment',
  name: 'comment',
  defaultContent: 'N/A'
}];
var data = function data(d) {
  d.start_date = $('#start_date').val();
  d.end_date = $('#end_date').val();
  d.status = $('#enq_status').val();
  d.department = $('#department_filter').val();
  d.classroom = $('#classroom-filter').val();
};
var table = commonDatatable('#enquiry_table', enquiryRoute.index, columns, data);
function tablescroll() {
  $('html, body').animate({
    scrollTop: $("#enquiry_table").offset().top
  }, 1000);
}
$("#filter").on('click', function (event) {
  event.preventDefault();
  tablescroll();
  table.draw();
});
$('#filterreset').click(function () {
  event.preventDefault();
  $('#start_date').val("");
  $('#end_date').val("");
  $('#status').val("").trigger('change');
  $('#department_filter').val("").trigger('change');
  $('#classroom-filter').val("").trigger('change');
  tablescroll();
  table.draw();
});
$(".nav-status").on('click', function (event) {
  event.preventDefault();
  $('#enq_status').val($(this).attr('data-value'));
  tablescroll();
  table.draw();
});
$(document).on('click', '.deleteEnquiry', function () {
  var enquiryID = $(this).attr('data-enquiryID');
  var url = enquiryRoute["delete"];
  url = url.replace(':enquiryID', enquiryID);
  var params = $.extend({}, doAjax_params_default);
  params['url'] = url;
  params['requestType'] = "DELETE";
  params['successCallbackFunction'] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on("click", "#addEnquiryEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = enquiryRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Enquiry");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editEnquiryEntry", function () {
  var editdid = $(this).attr("data-editEnquiryid");
  var url = enquiryRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Enquiry");
    $("#createContent").html(result);
    $('#department').trigger('change');
    setTimeout(function () {
      $('#classroom').val($('#classroom_val').val()).trigger('change');
    }, 1000);
  };
  commonAjax(params);
});
$(document).on("change", ".enquiry-status", function () {
  var editid = $(this).attr("data-statusEnquiryid");
  $(".enq-status").text($(this).val());
  $("#enquiry_status").val($(this).val());
  if ($(this).val() == "SHORTLISTED") {
    $("#enqFeesPayment").modal("show");
    $("#paid_amount").val(enquiryRoute.shortlistedFee);
    $('#payment_date').val(moment().format('YYYY-MM-DD')).trigger('change');
    $('#fees_enquiry_id').val(editid).trigger('change');
  } else if ($(this).val() == "ADMITTED") {
    $("#enqFeesPayment").modal("show");
    $("#paid_amount").val(enquiryRoute.admissiondFee);
    $('#payment_date').val(moment().format('YYYY-MM-DD')).trigger('change');
    $('#fees_enquiry_id').val(editid).trigger('change');
  } else {
    var params = $.extend({}, doAjax_params_default);
    var url = enquiryRoute.statusupdate;
    url = url.replace(":editid", editid);
    params["requestType"] = "PATCH";
    params["url"] = url;
    params["data"] = {
      status: $(this).val()
    };
    params["successCallbackFunction"] = function successCallbackFunction(result) {
      toastr.success(result.success);
      table.draw();
    };
    commonAjax(params);
  }
});
$("#department_filter").change(function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = enquiryRoute.getclassroomlist;
  params["requestType"] = "GET";
  params["data"] = {
    department: $(this).val()
  };
  params["successCallbackFunction"] = function successCallbackFunction(response) {
    var select = $(".classroom-filter");
    select.empty();
    select.append($('<option value="">Select Classroom</option>'));
    $.each(response.classlist, function (index, value) {
      select.append($('<option value="' + value.id + '">' + value.class_name + "</option>"));
    });
  };
  commonAjax(params);
});
$(document).ready(function () {
  $(".check-field").hide();
  $(".reference-field").hide();
  $("#payment_mode").change(function () {
    var selectedOption = $(this).val();
    if (selectedOption == "CHEQUE") {
      $(".check-field").show();
      $(".reference-field").hide();
    } else if (selectedOption == "ONLINE" || selectedOption == "GATEWAY") {
      $(".reference-field").show();
      $(".check-field").hide();
    } else {
      $(".check-field").hide();
      $(".reference-field").hide();
    }
  });
});
$("#enquiryfees_form").submit(function () {
  event.preventDefault();
  var form = $(this)[0];
  if ($(this).valid()) {
    ajaxHandler(form, enquiryRoute.storeFees, 'post', '#enquiryfees_form', '#saveenquiry', '#enqFeesPayment', '#enquiry_table');
    return false;
  }
});
$(document).on("click", ".exportData", function () {
  var url = enquiryRoute["export"];
  var data = {
    start_date: $('#start_date').val(),
    end_date: $('#end_date').val(),
    status: $('#enq_status').val(),
    department: $('#department_filter').val(),
    classroom: $('#classroom-filter').val()
  };
  exportData(url, data);
});
/******/ })()
;