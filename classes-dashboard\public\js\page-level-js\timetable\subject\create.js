/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Subject/resources/views/js/create.js ***!
  \******************************************************/
$("#createsubject_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createsubjectRoute.store, 'post', '#createsubject_form', '#savesubject', '#newSubjectEntry', '#subject_table');
    return false;
  }
});
/******/ })()
;