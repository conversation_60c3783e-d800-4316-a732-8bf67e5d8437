'use client';

import Image from 'next/image';
import React, { useRef, useEffect } from 'react';

type PosterDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function PosterDialog({ open, onClose }: PosterDialogProps) {
  const dialogRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (open && !dialog.open) {
      dialog.showModal();
    } else if (!open && dialog.open) {
      dialog.close();
    }
  }, [open]);

  return (
<dialog
      ref={dialogRef}
      className="w-[600px] h-[600px] p-0 border-none shadow-xl rounded-lg backdrop:bg-black/50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      onClose={onClose}
    >
      <div className="relative w-full h-full">
        {/* Close button */}
        <button
          className="absolute top-3 right-3 text-gray-600 hover:text-red-500 text-2xl z-10"
          onClick={onClose}
        >
          ×
        </button>
        <Image
          src="/MathsMarvelWinner.png"
          alt="Uwhiz Winner"
          width={400}
          height={250}
          className="w-full h-full object-cover rounded-lg"
        />
      </div>
    </dialog>

  );
}