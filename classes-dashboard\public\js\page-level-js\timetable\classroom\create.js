/******/ (() => { // webpackBootstrap
/*!********************************************************!*\
  !*** ./modules/Classroom/resources/views/js/create.js ***!
  \********************************************************/
$("#createclassroom_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createclassroomRoute.store, 'post', '#createclassroom_form', '#saveclassroom', '#newClassroomEntry', '#classroom_table');
    return false;
  }
});
/******/ })()
;