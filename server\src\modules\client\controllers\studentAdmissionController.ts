import { Request, Response } from "express";
import { 
  createStudentAdmissionService, 
  getStudentAdmissionById, 
  getAllStudentAdmissions,
  updateStudentAdmissionStatus 
} from "../services/studentAdmissionService";
import { CreateStudentAdmissionInput } from "../requests/studentAdmissionRequest";
import { Status, NotificationType } from "@prisma/client";
import { createAdminNotification } from "@/utils/notifications";
import { sendError } from "@/utils/response";

export const createStudentAdmission = async (req: Request, res: Response): Promise<any> => {
  try {
    const admissionData: CreateStudentAdmissionInput = req.body;
    
    // Handle photo upload if present
    const photo = req.file?.path;
    
    // Create student admission
    const result = await createStudentAdmissionService({
      ...admissionData,
      photo,
    });

    // Create notification for admin about new student admission
    await createAdminNotification({
      type: NotificationType.ADMIN_CONTENT_REVIEW_REQUIRED,
      title: 'New Student Admission',
      message: `A new student admission for ${admissionData.first_name} ${admissionData.last_name} (GR No: ${admissionData.gr_no}) has been submitted for review.`,
      data: {
        studentId: result.student.id,
        studentName: `${admissionData.first_name} ${admissionData.last_name}`,
        grNo: admissionData.gr_no,
        email: admissionData.email,
        classroom: admissionData.classroom,
        department: admissionData.department,
      }
    });

    res.status(201).json({
      success: true,
      message: "Student admission created successfully",
      data: {
        student: result.student,
        profile: result.studentProfile,
        parentDetails: result.parentDetails,
      },
    });
  } catch (error: any) {
    console.error("Error in createStudentAdmission:", error);
    return sendError(res, error.message || "Failed to create student admission", 500);
  }
};

export const getStudentAdmissionByIdHandler = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    
    const studentAdmission = await getStudentAdmissionById(id);
    
    if (!studentAdmission) {
      return sendError(res, "Student admission not found", 404);
    }

    res.status(200).json({
      success: true,
      data: studentAdmission,
    });
  } catch (error: any) {
    console.error("Error in getStudentAdmissionByIdHandler:", error);
    return sendError(res, error.message || "Failed to get student admission", 500);
  }
};

export const getAllStudentAdmissionsHandler = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as Status;

    const result = await getAllStudentAdmissions({ page, limit, status });

    res.status(200).json({
      success: true,
      data: result.students,
      pagination: {
        page: result.currentPage,
        limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error: any) {
    console.error("Error in getAllStudentAdmissionsHandler:", error);
    return sendError(res, error.message || "Failed to get student admissions", 500);
  }
};

export const updateStudentAdmissionStatusHandler = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const updatedProfile = await updateStudentAdmissionStatus(id, status);

    res.status(200).json({
      success: true,
      message: "Student admission status updated successfully",
      data: updatedProfile,
    });
  } catch (error: any) {
    console.error("Error in updateStudentAdmissionStatusHandler:", error);
    return sendError(res, error.message || "Failed to update student admission status", 500);
  }
};
