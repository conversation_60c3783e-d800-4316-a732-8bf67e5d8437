
<?php $__env->startSection('content'); ?>
<style>
    .firstcap {
        text-transform: capitalize;
    }
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Admission</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body p-0">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step" data-target="#student-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="student-details" id="student-details-trigger">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">Student Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#parents-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="parents-details" id="parents-details-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">Parents Details</span>
                                    </button>
                                </div>
                            </div>
                            <div class="bs-stepper-content">
                                <form id="student-admission-form" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>
                                    <?php echo $__env->make('Admission::steps.student-details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <?php echo $__env->make('Admission::steps.parents-details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <button type="button" class="btn btn-primary d-none" id="final-submit-btn">Submit</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<script>
    var admissionCreateRoute = {
        storeStudentDetails: "<?php echo e(route('storeStudentDetails')); ?>",
        storeStudentParentsDetails: "<?php echo e(route('storeStudentParentsDetails')); ?>",
    };
    window.originalStudentDetailsSubmit = window.studentDetailsSubmit;
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/Admission/js/create.js'))); ?>"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    });
</script>
<script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/toastr.min.js')); ?>"></script>
<script>
    $(document).ready(function() {
        $('#photoInput').on('change', function() {
            const file = this.files[0];
            if (file) {
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
                if (!allowedTypes.includes(file.type)) {
                    toastr.error('Only JPG, JPEG, and PNG files are allowed');
                    $(this).val('');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#photoPreview').attr('src', e.target.result).show();
                };
                reader.readAsDataURL(file);
            }
        });
    });
</script>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            $('#submit-student-details').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Student details Save & Next clicked');

                const studentForm = $('#student-details-forms');

            // Only fields with * asterisk are required
            const requiredFields = ['department', 'classroom', 'gr_no', 'first_name', 'middle_name', 'last_name', 'gender', 'contact_no'];
            let isValid = true;
            let missingFields = [];

            requiredFields.forEach(function(fieldName) {
                let field;
                if (fieldName === 'gender') {
                    // Special handling for radio buttons
                    field = studentForm.find('input[name="gender"]:checked');
                    if (field.length === 0) {
                        isValid = false;
                        missingFields.push('Gender');
                    }
                } else {
                    field = studentForm.find('[name="' + fieldName + '"]');
                    if (field.length && !field.val().trim()) {
                        isValid = false;
                        missingFields.push(fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                    }
                }
            });

            if (isValid) {
                console.log('Validation passed, saving student details to server');

                // Save student details to Node.js server
                saveStudentDetails();
            } else {
                console.log('Validation failed:', missingFields);
                toastr.error('Please fill all required fields: ' + missingFields.join(', '));
            }
        });

        $('#submit-parents-details').on('click', function(e) {
            e.preventDefault();
            const parentForm = $('#parents-details-forms');

            // Only parent fields with * asterisk are required
            const requiredParentFields = ['fathers_name', 'fathers_middle_name', 'fathers_last_name', 'mothers_name', 'mothers_middle_name', 'mothers_last_name', 'contact_no_1'];
            let isParentValid = true;
            let missingParentFields = [];

            requiredParentFields.forEach(function(fieldName) {
                const field = parentForm.find('[name="' + fieldName + '"]');
                if (field.length && !field.val().trim()) {
                    isParentValid = false;
                    missingParentFields.push(fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                }
            });

            if (isParentValid) {
                console.log('Parent validation passed, saving parent details');
                saveParentDetails();
            } else {
                console.log('Parent validation failed:', missingParentFields);
                toastr.error('Please fill all required parent fields: ' + missingParentFields.join(', '));
            }
        });
        }, 500); // Increased delay to ensure all scripts load properly
    });

        // Prevent all form submissions to avoid page reload
        $('form').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submission prevented:', $(this).attr('id'));
            return false;
        });

        // Save student details (Step 1)
        function saveStudentDetails() {
            const studentForm = document.getElementById('student-details-forms');
            const formData = new FormData(studentForm);

            console.log('Submitting student details to API...');

            // Send student details to Node.js API
            $.ajax({
                url: '<?php echo e(env('UEST_FRONTEND_URL')); ?>/api/v1/student-profile/admission',
                method: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhrFields: {
                    withCredentials: true
                },
                success: function(response) {
                    console.log('Student details saved successfully:', response);

                    // Store student ID for next step
                    window.currentStudentId = response.data?.student?.id;

                    toastr.success('Student details saved! Please fill parent details.');

                    // Move to next step
                    setTimeout(function() {
                        stepper.next();
                    }, 500);
                },
                error: function(xhr) {
                    console.error('Error saving student details:', xhr);
                    const message = xhr.responseJSON?.message || 'Failed to save student details.';
                    toastr.error(message);
                }
            });
        }

        // Save parent details (Step 2)
        function saveParentDetails() {
            const parentForm = document.getElementById('parents-details-forms');
            const formData = new FormData(parentForm);

            // Add student ID from previous step
            if (window.currentStudentId) {
                formData.append('student_id', window.currentStudentId);
            }

            console.log('Submitting parent details to API...');

            // Send parent details to Node.js API
            $.ajax({
                url: '<?php echo e(env('UEST_FRONTEND_URL')); ?>/api/v1/student-profile/parent-details',
                method: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhrFields: {
                    withCredentials: true
                },
                success: function(response) {
                    console.log('Parent details saved successfully:', response);

                    // Save metadata to Laravel
                    saveLaravelMetadata();
                },
                error: function(xhr) {
                    console.error('Error saving parent details:', xhr);
                    const message = xhr.responseJSON?.message || 'Failed to save parent details.';
                    toastr.error(message);
                }
            });
        }

        // Save metadata to Laravel (Final step)
        function saveLaravelMetadata() {
            const studentForm = $('#student-details-forms');

            $.ajax({
                url: '<?php echo e(route('storeStudentDetails')); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    class_uuid: studentForm.find('[name="classroom"]').val(),
                    gr_no: studentForm.find('[name="gr_no"]').val(),
                    uid_no: studentForm.find('[name="uid_no"]').val(),
                    student_id: window.currentStudentId,
                    year: studentForm.find('[name="year"]').val(),
                },
                success: function() {
                    toastr.success('Student admission completed successfully! All data has been saved.');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                },
                error: function(xhr) {
                    console.warn('Laravel metadata save failed:', xhr);
                    toastr.warning('Student data saved successfully, but metadata save failed: ' + (xhr.responseJSON?.message || 'Unknown error'));
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            });
        }

        function submitCompleteForm() {
            const formData = new FormData();

            $('#student-details-forms input, #student-details-forms select, #student-details-forms textarea').each(function() {
                const input = $(this);
                const name = input.attr('name');
                const type = input.attr('type');

                if (name) {
                    if (type === 'radio' || type === 'checkbox') {
                        if (input.is(':checked')) {
                            formData.append(name, input.val());
                        }
                    } else if (type === 'file') {
                        if (input[0].files.length > 0) {
                            formData.append(name, input[0].files[0]);
                        }
                    } else {
                        formData.append(name, input.val());
                    }
                }
            });

            $('#parents-details-forms input, #parents-details-forms select, #parents-details-forms textarea').each(function() {
                const input = $(this);
                const name = input.attr('name');
                const type = input.attr('type');

                if (name) {
                    if (type === 'radio' || type === 'checkbox') {
                        if (input.is(':checked')) {
                            formData.append(name, input.val());
                        }
                    } else if (type === 'file') {
                        if (input[0].files.length > 0) {
                            formData.append(name, input[0].files[0]);
                        }
                    } else {
                        formData.append(name, input.val());
                    }
                }
            });

            $.ajax({
                    url: '<?php echo e(env('UEST_FRONTEND_URL')); ?>/api/v1/student-profile/admission',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function(response) {
                        console.log('Student admission created successfully:', response);

                        $.ajax({
                            url: '<?php echo e(route('storeStudentDetails')); ?>',
                            method: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>',
                                class_uuid: formData.get('class_uuid'),
                                gr_no: formData.get('gr_no'),
                                uid_no: formData.get('uid_no'),
                                student_id: response.data?.student?.id || formData.get('student_id'),
                                year: formData.get('year'),
                            },
                            success: function() {
                                toastr.success('Student admission submitted successfully! The student profile and parent details have been created and are pending review.');
                                $('#photoPreview').hide();
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            },
                            error: function(xhr) {
                                console.warn('Laravel metadata save failed:', xhr);
                                toastr.warning('Student created successfully, but metadata save failed: ' + (xhr.responseJSON?.message || 'Unknown error'));
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            }
                        });
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'Failed to submit student details.';
                        toastr.error(message);
                        console.error('Error:', xhr);
                    }
                });
        }

        }, 500); // Increased delay to ensure all scripts load properly
    });
</script>
<?php echo JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-admission-form'); ?>

<?php echo JsValidator::formRequest('Admission\Http\Requests\CreateStudentParentsDetailsRequest', '#student-admission-form'); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/create.blade.php ENDPATH**/ ?>