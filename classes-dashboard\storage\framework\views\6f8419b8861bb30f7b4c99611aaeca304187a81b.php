
<?php $__env->startSection('content'); ?>
<style>
    .firstcap {
        text-transform: capitalize;
    }
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Admission</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body p-0">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step" data-target="#student-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="student-details" id="student-details-trigger">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">Student Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#parents-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="parents-details" id="parents-details-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">Parents Details</span>
                                    </button>
                                </div>
                            </div>
                            <div class="bs-stepper-content">
                                <form id="student-admission-form" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>
                                    <?php echo $__env->make('Admission::steps.student-details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <?php echo $__env->make('Admission::steps.parents-details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<script>
    var admissionCreateRoute = {
        storeStudentDetails: "<?php echo e(route('storeStudentDetails')); ?>",
        storeStudentParentsDetails: "<?php echo e(route('storeStudentParentsDetails')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/Admission/js/create.js'))); ?>"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    });
</script>
<script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
<script src="<?php echo e(asset('assets/js/toastr.min.js')); ?>"></script>
<script>
    $(document).ready(function() {
        $('#photoInput').on('change', function() {
            const file = this.files[0];
            if (file) {
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
                if (!allowedTypes.includes(file.type)) {
                    toastr.error('Only JPG, JPEG, and PNG files are allowed');
                    $(this).val('');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#photoPreview').attr('src', e.target.result).show();
                };
                reader.readAsDataURL(file);
            }
        });
    });
</script>
<script>
    // Wait for DOM and all scripts to load, then override button handlers
    $(document).ready(function() {
        // Small delay to ensure other scripts have loaded
        setTimeout(function() {
            // Override existing button handlers
            $('#submit-student-details').off('click').on('click', function(e) {
            e.preventDefault();

            // Validate only student details form
            const studentForm = $('#student-details-forms');
            if (studentForm.valid()) {
                // Just move to next step without API call
                toastr.success('Student details saved! Please fill parent details.');
                stepper.next();
            }
        });

        // Override existing parent details button handler
        $('#submit-parents-details').off('click').on('click', function(e) {
            e.preventDefault();

            // Validate parent details form
            const parentForm = $('#parents-details-forms');
            if (parentForm.valid()) {
                // Submit the complete form now
                $('#student-admission-form').submit();
            }
        });
        }, 100); // Small delay to ensure other scripts load first
    });

        $('#student-admission-form').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            if ($(this).valid()) {
                $.ajax({
                    url: '<?php echo e(env('UEST_FRONTEND_URL')); ?>/api/v1/student-profile/admission',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function(response) {
                        console.log('Student admission created successfully:', response);

                        // Save minimal metadata to Laravel (only 3 fields)
                        $.ajax({
                            url: '<?php echo e(route('storeStudentDetails')); ?>',
                            method: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>',
                                class_uuid: formData.get('class_uuid'),
                                gr_no: formData.get('gr_no'),
                                uid_no: formData.get('uid_no'),
                                student_id: response.data?.student?.id || formData.get('student_id'),
                                year: formData.get('year'),
                            },
                            success: function() {
                                toastr.success('Student admission submitted successfully! The student profile and parent details have been created and are pending review.');
                                $('#photoPreview').hide();
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            },
                            error: function(xhr) {
                                console.warn('Laravel metadata save failed:', xhr);
                                toastr.warning('Student created successfully, but metadata save failed: ' + (xhr.responseJSON?.message || 'Unknown error'));
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            }
                        });
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'Failed to submit student details.';
                        toastr.error(message);
                        console.error('Error:', xhr);
                    }
                });
            }
        });
    });
</script>
<?php echo JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-admission-form'); ?>

<?php echo JsValidator::formRequest('Admission\Http\Requests\CreateStudentParentsDetailsRequest', '#student-admission-form'); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/create.blade.php ENDPATH**/ ?>