@extends('layouts.app')
@section('content')
<style>
    .firstcap 
    {
        text-transform:capitalize;
    }
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Admission</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body p-0">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <!-- your steps here -->
                                <div class="step" data-target="#student-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="student-details" id="student-details-trigger">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">Student Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#parents-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="parents-details" id="parents-details-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">Parents Details</span>
                                    </button>
                                </div>
                            </div>
                            <div class="bs-stepper-content">
                            @include('Admission::steps.student-details')
                            @include('Admission::steps.parents-details')
                            </div>
                        </div>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script src="
https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'))
    })
</script>
<script>
     var admissionCreateRoute = {
        storeStudentDetails: "{{ route('storeStudentDetails') }}",
        storeStudentParentsDetails: "{{ route('storeStudentParentsDetails') }}",
    };
    window.currentStudentId = null;

    $(document).ready(function() {

        $('#submit-student-details').off('click').on('click', function(e) {
            e.preventDefault();

            const form = $('#student-details-forms');
            const formData = new FormData(form[0]);

            if (form.valid()) {
                console.log('Submitting student details directly to Node.js API...');

                // Debug: Log form data
                for (let pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                $.ajax({
                    url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/admission/step-one',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        withCredentials: true
                    },
                    beforeSend: function() {
                        $('.page-loader').show();
                    },
                    success: function(response) {
                        console.log('Student details saved successfully:', response);

                        // Store student ID for next step
                        window.currentStudentId = response.data?.student?.id;

                        // Set student ID in parent form
                        $('#parents-details-forms input[name="student_id"]').val(window.currentStudentId);

                        toastr.success('Student details saved! Please fill parent details.');

                        // Move to next step
                        setTimeout(function() {
                            stepper.next();
                        }, 500);
                    },
                    error: function(xhr) {
                        console.error('Error saving student details:', xhr);
                        const message = xhr.responseJSON?.message || 'Failed to save student details.';
                        toastr.error(message);
                    },
                    complete: function() {
                        $('.page-loader').hide();
                    }
                });
            }
        });

        // Step 2: Parent Details - Direct API Call to Node.js
        $('#submit-parents-details').off('click').on('click', function(e) {
            e.preventDefault();

            const form = $('#parents-details-forms');
            const formData = new FormData(form[0]);

            // Set student ID from previous step (replace existing empty value)
            if (window.currentStudentId) {
                // Remove existing student_id from formData if present
                formData.delete('student_id');
                formData.append('student_id', window.currentStudentId);
                console.log('Student ID set for parent details:', window.currentStudentId);
            }

            if (form.valid()) {
                console.log('Submitting parent details directly to Node.js API...');

                $.ajax({
                    url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/admission/parent-details',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        withCredentials: true
                    },
                    beforeSend: function() {
                        $('.page-loader').show();
                    },
                    success: function(response) {
                        console.log('Parent details saved successfully:', response);

                        // Save metadata to Laravel (simple approach)
                        const studentForm = $('#student-details-forms');

                        $.ajax({
                            url: '{{ route('storeStudentDetails') }}',
                            method: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                class_uuid: studentForm.find('[name="classroom"]').val(),
                                gr_no: studentForm.find('[name="gr_no"]').val(),
                                uid_no: studentForm.find('[name="gr_no"]').val(),
                                student_id: window.currentStudentId,
                                year: studentForm.find('[name="year"]').val(),
                            },
                            success: function(laravelResponse) {
                                console.log('Laravel metadata saved:', laravelResponse);
                                toastr.success('Student admission completed successfully!');
                                setTimeout(() => location.reload(), 2000);
                            },
                            error: function(xhr) {
                                console.error('Laravel save failed:', xhr);
                                toastr.warning('Student data saved, but Laravel metadata failed');
                                setTimeout(() => location.reload(), 2000);
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('Error saving parent details:', xhr);
                        const message = xhr.responseJSON?.message || 'Failed to save parent details.';
                        toastr.error(message);
                        $('.page-loader').hide();
                    }
                });
            }
        });


    });
</script>
<script src="{{ asset(mix('js/page-level-js/Admission/js/create.js')) }}"></script>
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-details-forms') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentParentsDetailsRequest', '#parents-details-forms') !!}

@if(isset($data))
<script>
    $('.firststep').removeClass('d-none');
    let href = $('.gotourl').attr('href').replace(':id', {{$data->id}});
    $('.gotourl').attr('href', href);

    var data = {!! json_encode($data); !!};
    $.each(data, function(fieldName, value) {
        if(fieldName != "photo") {
            $('[name=' + fieldName + ']').val(value);
        } else {
            $('.image-product-logo').attr('src', window.location.origin + checkHost() + "/storage/student_img/"+value);
        }
    });

    var academicdata = {!! json_encode($data->getAcademicInfo); !!};
    $.each(academicdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    var parentdata = {!! json_encode($data->getParentInfo); !!};
    $.each(parentdata, function(fieldName, value) {
        $('[name=' + fieldName + ']').val(value);
    });

    $('[name="student_id"]').val("{{$data->id}}");
    $('#department').trigger('change');
    $('#waypoint').trigger('change');

    setTimeout(function (){
        console.log("{{ $data->getAcademicInfo->route }}")
            $('#route').val("{{ $data->getAcademicInfo->route }}").trigger('change');          
    }, 2000);

    setTimeout(function (){
            $('#classroom').val("{{ $data->getAcademicInfo->classroom }}").trigger('change');    
    }, 2000);
</script>
@endif
@endsection