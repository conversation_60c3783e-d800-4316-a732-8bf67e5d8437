@extends('layouts.app')
@section('content')
<style>
    .firstcap {
        text-transform: capitalize;
    }
</style>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Admission</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body p-0">
                        <div class="bs-stepper">
                            <div class="bs-stepper-header" role="tablist">
                                <div class="step" data-target="#student-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="student-details" id="student-details-trigger">
                                        <span class="bs-stepper-circle">1</span>
                                        <span class="bs-stepper-label">Student Details</span>
                                    </button>
                                </div>
                                <div class="line"></div>
                                <div class="step" data-target="#parents-details">
                                    <button type="button" class="step-trigger" role="tab" aria-controls="parents-details" id="parents-details-trigger">
                                        <span class="bs-stepper-circle">2</span>
                                        <span class="bs-stepper-label">Parents Details</span>
                                    </button>
                                </div>
                            </div>
                            <div class="bs-stepper-content">
                                <form id="student-admission-form" enctype="multipart/form-data">
                                    @csrf
                                    @include('Admission::steps.student-details')
                                    @include('Admission::steps.parents-details')
                                    <button type="button" class="btn btn-primary d-none" id="final-submit-btn">Submit</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
<script>
    var admissionCreateRoute = {
        storeStudentDetails: "{{ route('storeStudentDetails') }}",
        storeStudentParentsDetails: "{{ route('storeStudentParentsDetails') }}",
    };

    // Disable the original create.js functionality
    window.studentDetailsSubmit = function() {
        console.log('Original studentDetailsSubmit disabled');
        return false;
    };
</script>
<!-- Commented out to prevent conflicts -->
<!-- <script src="{{ asset(mix('js/page-level-js/Admission/js/create.js')) }}"></script> -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    });
</script>
<script src="{{ asset('assets/js/jquery.min.js') }}"></script>
<script src="{{ asset('assets/js/toastr.min.js') }}"></script>
<script>
    $(document).ready(function() {
        $('#photoInput').on('change', function() {
            const file = this.files[0];
            if (file) {
                const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
                if (!allowedTypes.includes(file.type)) {
                    toastr.error('Only JPG, JPEG, and PNG files are allowed');
                    $(this).val('');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#photoPreview').attr('src', e.target.result).show();
                };
                reader.readAsDataURL(file);
            }
        });
    });
</script>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            $('#submit-student-details').off('click');
            $('#submit-parents-details').off('click');

            $('#submit-student-details').on('click', function(e) {
            e.preventDefault();

            const studentForm = $('#student-details-forms');

            // Only fields with * asterisk are required
            const requiredFields = ['department', 'classroom', 'gr_no', 'first_name', 'middle_name', 'last_name', 'gender', 'contact_no'];
            let isValid = true;
            let missingFields = [];

            requiredFields.forEach(function(fieldName) {
                let field;
                if (fieldName === 'gender') {
                    // Special handling for radio buttons
                    field = studentForm.find('input[name="gender"]:checked');
                    if (field.length === 0) {
                        isValid = false;
                        missingFields.push('Gender');
                    }
                } else {
                    field = studentForm.find('[name="' + fieldName + '"]');
                    if (field.length && !field.val().trim()) {
                        isValid = false;
                        missingFields.push(fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                    }
                }
            });

            if (isValid) {
                toastr.success('Student details saved! Please fill parent details.');
                stepper.next();
            } else {
                toastr.error('Please fill all required fields: ' + missingFields.join(', '));
            }
        });

        $('#submit-parents-details').on('click', function(e) {
            e.preventDefault();
            const parentForm = $('#parents-details-forms');

            // Only parent fields with * asterisk are required
            const requiredParentFields = ['fathers_name', 'fathers_middle_name', 'fathers_last_name', 'mothers_name', 'mothers_middle_name', 'mothers_last_name', 'contact_no_1'];
            let isParentValid = true;
            let missingParentFields = [];

            requiredParentFields.forEach(function(fieldName) {
                const field = parentForm.find('[name="' + fieldName + '"]');
                if (field.length && !field.val().trim()) {
                    isParentValid = false;
                    missingParentFields.push(fieldName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()));
                }
            });

            if (isParentValid) {
                submitCompleteForm();
            } else {
                toastr.error('Please fill all required parent fields: ' + missingParentFields.join(', '));
            }
        });
        }, 500); // Increased delay to ensure all scripts load properly
    });

        // Prevent any form submission that might cause page reload
        $('#student-admission-form').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submission prevented to avoid page reload');
            return false;
        });

        // Handle manual form submission when needed
        function submitCompleteForm() {
            const form = document.getElementById('student-admission-form');
            const formData = new FormData(form);

            $.ajax({
                    url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/student-profile/admission',
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function(response) {
                        console.log('Student admission created successfully:', response);

                        $.ajax({
                            url: '{{ route('storeStudentDetails') }}',
                            method: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                class_uuid: formData.get('class_uuid'),
                                gr_no: formData.get('gr_no'),
                                uid_no: formData.get('uid_no'),
                                student_id: response.data?.student?.id || formData.get('student_id'),
                                year: formData.get('year'),
                            },
                            success: function() {
                                toastr.success('Student admission submitted successfully! The student profile and parent details have been created and are pending review.');
                                $('#photoPreview').hide();
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            },
                            error: function(xhr) {
                                console.warn('Laravel metadata save failed:', xhr);
                                toastr.warning('Student created successfully, but metadata save failed: ' + (xhr.responseJSON?.message || 'Unknown error'));
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            }
                        });
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'Failed to submit student details.';
                        toastr.error(message);
                        console.error('Error:', xhr);
                    }
                });
            }
        }

        }, 500); // Increased delay to ensure all scripts load properly
    });
</script>
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-admission-form') !!}
{!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentParentsDetailsRequest', '#student-admission-form') !!}
@endsection