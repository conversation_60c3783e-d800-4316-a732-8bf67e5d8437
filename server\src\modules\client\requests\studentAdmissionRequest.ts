import { z } from "zod";

// Student admission validation schema
export const createStudentAdmissionSchema = z.object({
  // Student Details
  department: z.string().min(1, "Department is required"),
  classroom: z.string().min(1, "Classroom is required"),
  gr_no: z.string().min(1, "GR No is required"),
  family_name: z.string().optional(),
  first_name: z.string().min(1, "First name is required"),
  middle_name: z.string().min(1, "Middle name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.enum(["Male", "Female"], { errorMap: () => ({ message: "Gender must be Male or Female" }) }),
  contact_no: z.string().min(10, "Contact number must be at least 10 digits"),
  email: z.string().email("Invalid email format").optional(),
  age: z.string().optional(),
  date_of_birth: z.string().optional(),
  aadhaar_no: z.string().optional(),
  blood_group: z.enum(["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-", ""]).optional(),
  birth_place: z.string().optional(),
  mother_tongue: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  pin: z.string().optional(),
  district: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  sub_caste: z.string().optional(),

  // Parent Details
  fathers_name: z.string().min(1, "Father's name is required"),
  fathers_middle_name: z.string().min(1, "Father's middle name is required"),
  fathers_last_name: z.string().min(1, "Father's last name is required"),
  fathers_qualification: z.string().optional(),
  fathers_occupation: z.string().optional(),
  fathers_aadhaar_no: z.string().optional(),
  mothers_name: z.string().min(1, "Mother's name is required"),
  mothers_middle_name: z.string().min(1, "Mother's middle name is required"),
  mothers_last_name: z.string().min(1, "Mother's last name is required"),
  mothers_qualification: z.string().optional(),
  mothers_occupation: z.string().optional(),
  mothers_aadhaar_no: z.string().optional(),
  contact_no_1: z.string().min(10, "Primary contact number is required"),
  contact_no_2: z.string().optional(),
  family_income: z.string().optional(),
  part_of_ngo: z.string().optional(),

  // Additional fields
  year: z.string().optional(),
  class_uuid: z.string().optional(),
  uid_no: z.string().optional(),
  student_id: z.string().optional(),
});

export type CreateStudentAdmissionInput = z.infer<typeof createStudentAdmissionSchema>;
