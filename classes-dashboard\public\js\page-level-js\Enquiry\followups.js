/******/ (() => { // webpackBootstrap
/*!*********************************************************!*\
  !*** ./modules/Enquiry/resources/views/js/followups.js ***!
  \*********************************************************/
var columns = [{
  data: 'action',
  name: 'action',
  orderable: false
}, {
  data: 'date',
  name: 'date'
}, {
  data: 'notes',
  name: 'notes'
}, {
  data: 'created_by',
  name: 'created_by',
  defaultContent: 'N/A'
}, {
  data: 'updated_by',
  name: 'updated_by',
  defaultContent: 'N/A'
}, {
  data: 'status',
  name: 'status'
}];
var table = commonDatatable('#followp_table', followupRoute.index, columns);
$("#createfollowup_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    var params = $.extend({}, doAjax_params_default);
    var url = followupRoute.store;
    params["requestType"] = "POST";
    params["url"] = url;
    params["data"] = form.serialize();
    params["successCallbackFunction"] = function successCallbackFunction(result) {
      table.draw();
      $(form).get(0).reset();
      toastr.success(result.success);
      $('#newEnquiryFollowUpEntry').modal('hide');
    };
    commonAjax(params);
  }
});
$("body").delegate("#date", "focusin", function () {
  $(this).datepicker({
    dateFormat: 'yy-mm-dd',
    changeMonth: true,
    changeYear: true,
    minDate: new Date(startYearDate),
    maxDate: new Date(endYearDate)
  });
});
$("body").delegate("#time", "focusin", function () {
  $(this).datetimepicker({
    format: 'hh:mm A'
  });
});
$(document).on('click', '.folloup-status', function () {
  var params = $.extend({}, doAjax_params_default);
  var id = $(this).attr("data-statusFollowupid");
  var url = followupRoute.status;
  url = url.replace(":id", id);
  params["requestType"] = "PATCH";
  params["url"] = url;
  params["data"] = {
    'status': $(this).val()
  };
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    table.draw();
    toastr.success(result.success);
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on('click', '.deleteFollowp', function () {
  var id = $(this).attr('data-Followupid');
  var url = followupRoute["delete"];
  url = url.replace(':id', id);
  var params = $.extend({}, doAjax_params_default);
  params['url'] = url;
  params['requestType'] = "DELETE";
  params['successCallbackFunction'] = function successCallbackFunction(result) {
    table.draw();
    toastr.success(result.success);
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
/******/ })()
;