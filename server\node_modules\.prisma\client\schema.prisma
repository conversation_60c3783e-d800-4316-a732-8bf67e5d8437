generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Classes {
  id               String    @id @default(uuid())
  firstName        String
  lastName         String
  className        String?
  email            String    @unique
  password         String
  createdAt        DateTime  @default(now())
  contactNo        String?
  username         String    @unique
  resetToken       String? // Store reset token
  resetTokenExpiry DateTime?
  isVerified       Boolean   @default(false)

  experience      ClassesExpereince[]
  education       ClassesEducation[]
  certificates    ClassesCertificates[]
  ClassAbout      ClassesAbout?
  status          ClassesStatus?
  tuitionClasses  TuitionClass[]
  ExamApplication ExamApplication[]
  questionAnswers Question_answer[]
  classesThought  ClassesThought[]
  testimonials    Testimonial[]
  blogs           Blog[]
  savedByStudents StudentWishlist[]
  classesReviews  ClassesReviews[]
  address         ClassesAddress?
  viewLogs        StudentClassViewLog[]
}

model ClassesAbout {
  id             String   @id @default(uuid())
  birthDate      DateTime
  catchyHeadline String?
  tutorBio       String?
  profilePhoto   String?
  classesLogo    String?
  videoUrl       String?
  classId        String   @unique
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  class          Classes  @relation(fields: [classId], references: [id])
}

model AdminUser {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
}

model ClassesExpereince {
  id             String   @id @default(uuid())
  classId        String
  title          String?
  certificateUrl String?
  from           String?
  to             String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isExperience   Boolean  @default(true)
  class          Classes  @relation(fields: [classId], references: [id])
}

model ClassesEducation {
  id          String   @id @default(uuid())
  classId     String
  university  String?
  degree      String?
  degreeType  String?
  passoutYear String?
  certificate String?
  isDegree    Boolean  @default(false)
  createdAt   DateTime @default(now())
  class       Classes  @relation(fields: [classId], references: [id])
}

model ClassesCertificates {
  id             String   @id @default(uuid())
  classId        String
  title          String?
  certificateUrl String?
  isCertificate  Boolean  @default(true)
  createdAt      DateTime @default(now())
  class          Classes  @relation(fields: [classId], references: [id])
}

model ClassesStatus {
  id        String              @id @default(uuid())
  classId   String              @unique
  status    ClassApprovalStatus
  createdAt DateTime            @default(now())
  class     Classes             @relation(fields: [classId], references: [id])
}

model UestCoins {
  id        String   @id @default(uuid())
  modelId   String
  modelType String
  coins     Float
  createdAt DateTime @default(now())

  @@unique([modelId, modelType])
}

model UestCoinTransaction {
  id        String          @id @default(uuid())
  modelId   String
  modelType String
  amount    Float
  type      TransactionType
  reason    String?
  createdAt DateTime        @default(now())
}

model TuitionClass {
  id           String   @id @default(uuid())
  classId      String
  education    String
  boardType    String?
  subject      String?
  medium       String?
  section      String?
  coachingType String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  details      String?
  class        Classes  @relation(fields: [classId], references: [id])
}

model ConstantCategory {
  id      Int              @id @default(autoincrement())
  name    String           @unique
  details ConstantDetail[]
}

model ConstantDetail {
  id         Int              @id @default(autoincrement())
  value      String
  categoryId Int
  category   ConstantCategory @relation(fields: [categoryId], references: [id])
}

enum ClassApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  IN_PROCESS
}

enum ModelType {
  CLASS
  STUDENT
  SCHOOL
}

enum TransactionType {
  CREDIT
  DEBIT
}

enum UserType {
  ADMIN
  CLASS
  STUDENT
}

model Exam {
  id                      Int               @id @default(autoincrement())
  exam_name               String
  start_date              DateTime
  duration                Int
  marks                   Decimal
  total_student_intake    Int
  level                   String
  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
  total_questions         Int
  coins_required          Int?
  start_registration_date DateTime?
  exam_type               ExamType?
  question_paper          Question_paper[]
  ExamApplication         ExamApplication[]
  UwhizPriceRank          UwhizPriceRank[]
}

model UwhizPriceRank {
  id     String @id @default(uuid())
  examId Int
  rank   Int
  price  Int
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
}

enum ExamType {
  CLASSES
  STUDENTS
}

model Question_paper {
  id              Int               @id @default(autoincrement())
  question        String
  optionOne       String
  optionTwo       String
  optionThree     String
  optionFour      String
  correctAns      String
  examId          Int
  question_answer Question_answer[]
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  exam            Exam              @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model Question_answer {
  id         Int      @id @default(autoincrement())
  userId     String
  questionId Int
  answer     String
  isCorrect  Boolean  @default(false)
  createdAt  DateTime @default(now())

  question Question_paper @relation(fields: [questionId], references: [id], onDelete: Cascade)
  user     Classes        @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([questionId])
}

model ExamApplication {
  id        String   @id @default(uuid())
  examId    Int
  classId   String
  createdAt DateTime @default(now())
  exam      Exam     @relation(fields: [examId], references: [id])
  class     Classes  @relation(fields: [classId], references: [id])

  @@unique([examId, classId])
}

model ClassesThought {
  id        String   @id @default(uuid())
  classId   String
  thoughts  String
  status    Status   @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Classes  @relation(fields: [classId], references: [id])
}

model Testimonial {
  id        String   @id @default(uuid())
  classId   String
  userId    String?
  message   String
  rating    Int
  status    Status   @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Classes  @relation(fields: [classId], references: [id])

  @@index([classId])
}

enum Status {
  PENDING
  APPROVED
  REJECTED
}

model Blog {
  id              String   @id @default(uuid())
  classId         String
  blogTitle       String
  blogImage       String
  blogDescription String
  status          Status   @default(PENDING)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  class           Classes  @relation(fields: [classId], references: [id])

  @@index([classId])
}

model Student {
  id               String    @id @default(uuid())
  firstName        String
  lastName         String
  email            String    @unique
  contact          String?
  password         String
  googleId         String?   @unique
  profilePhoto     String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  resetToken       String?
  resetTokenExpiry DateTime?
  isVerified       Boolean   @default(false)

  wishlist                  StudentWishlist[]
  studentReviews            ClassesReviews[]
  profile                   StudentProfile?
  studentparentDetails      StudentParentDetails?
  smWaterParkTickets        SMWaterParkTicket[]
  shivWaterParkTickets      ShivWaterParkTicket[]
  smWaterParkAdminEntries   SMWaterParkAdminEntry[]
  shivWaterParkAdminEntries ShivWaterParkAdminEntry[]
  classViewLogs             StudentClassViewLog[]
}

model StudentProfile {
  id          String    @id @default(uuid())
  studentId   String    @unique
  medium      String?
  classroom   String?
  birthday    DateTime?
  school      String?
  address     String?
  photo       String?
  documentUrl String?
  status      Status    @default(PENDING)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  family_name   String?
  first_name    String?
  middle_name   String?
  last_name     String?
  gender        String?
  date_of_birth DateTime?
  age           Int?
  aadhaar_no    BigInt?
  blood_group   String?
  birth_place   String?
  mother_tongue String?
  city          String?
  pin           BigInt?
  district      String?
  state         String?
  country       String?
  religion      String?
  caste         String?
  sub_caste     String?
  contact_no    BigInt?
  email         String?

  student Student @relation(fields: [studentId], references: [id])
}

model StudentParentDetails {
  id                    String   @id @default(uuid())
  studentId             String   @unique
  fathers_name          String?
  fathers_middle_name   String?
  fathers_last_name     String?
  fathers_qualification String?
  fathers_occupation    String?
  fathers_aadhaar_no    String?
  mothers_name          String?
  mothers_middle_name   String?
  mothers_last_name     String?
  mothers_qualification String?
  mothers_occupation    String?
  mothers_aadhaar_no    String?
  contact_no_1          String?
  contact_no_2          String?
  family_income         String?
  part_of_ngo           String?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  student Student @relation(fields: [studentId], references: [id])

  @@index([studentId])
}

model StudentWishlist {
  id           String   @id @default(uuid())
  studentId    String
  savedClassId String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  student    Student @relation(fields: [studentId], references: [id])
  savedClass Classes @relation(fields: [savedClassId], references: [id])

  @@unique([studentId, savedClassId])
  @@index([studentId])
  @@index([savedClassId])
}

model ClassesReviews {
  id          String   @id @default(uuid())
  modelId     String
  modelType   String
  classId     String
  studentId   String?
  studentName String?
  userName    String?
  rating      Int
  message     String
  createdAt   DateTime @default(now())
  class       Classes  @relation(fields: [classId], references: [id])
  student     Student? @relation(fields: [studentId], references: [id])

  @@unique([modelId, modelType])
  @@index([classId])
  @@index([studentId])
  @@index([createdAt])
}

model ClassesCanApplyForQuestionBank {
  id          String  @id @default(uuid())
  classId     String
  hasEligible Boolean @default(false)
}

model ReferralLink {
  id        String   @id @default(uuid())
  userId    String // Admin/Class/Student ID
  userType  UserType // ADMIN, CLASS, STUDENT
  code      String   @unique // Unique referral code
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  referrals Referral[]

  @@index([userId, userType])
  @@index([code])
}

model Referral {
  id                String   @id @default(uuid())
  referralLinkId    String
  referredUserId    String // New user who registered
  referredUserType  UserType // CLASS or STUDENT
  referredUserName  String // Name of registered user
  referredUserEmail String // Email of registered user
  createdAt         DateTime @default(now())

  // Relations
  referralLink ReferralLink      @relation(fields: [referralLinkId], references: [id])
  earnings     ReferralEarning[] // One referral can have multiple earnings

  @@index([referralLinkId])
  @@index([referredUserId, referredUserType])
  @@index([createdAt])
}

// New table for tracking referral earnings
model ReferralEarning {
  id            String        @id @default(uuid())
  studentId     String // Student who was referred
  examId        String? // Exam ID for u-whiz applications (nullable for registration earnings)
  referralId    String // Reference to the Referral record
  earningType   EarningType // REGISTRATION or UWHIZ_APPLICATION
  amount        Float // Earning amount (10 for registration, 25 for u-whiz)
  paymentStatus PaymentStatus @default(UNPAID) // PAID or UNPAID
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  referral Referral @relation(fields: [referralId], references: [id])

  @@index([studentId])
  @@index([referralId])
  @@index([earningType])
  @@index([paymentStatus])
  @@index([createdAt])
}

// Enums for referral earning system
enum EarningType {
  REGISTRATION
  UWHIZ_APPLICATION
}

enum PaymentStatus {
  PAID
  UNPAID
}

model SMWaterParkTicket {
  id          String   @id @default(uuid())
  studentId   String
  ticketCode  String   @unique
  status      String   @default("Active")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  generatedAt DateTime @default(now())
  student     Student  @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([ticketCode])
  @@index([studentId, status])
}

model ShivWaterParkTicket {
  id          String   @id @default(uuid())
  studentId   String
  ticketCode  String   @unique
  status      String   @default("Active")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  generatedAt DateTime @default(now())
  student     Student  @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([ticketCode])
  @@index([studentId, status])
}

model SMWaterParkAdminEntry {
  id         String    @id @default(uuid())
  ticketCode String    @unique
  studentId  String
  visitDate  DateTime?
  enteredAt  DateTime  @default(now())
  enteredBy  String?
  student    Student   @relation(fields: [studentId], references: [id])

  @@index([ticketCode])
  @@index([studentId])
  @@index([enteredAt])
}

model ShivWaterParkAdminEntry {
  id         String    @id @default(uuid())
  ticketCode String    @unique
  studentId  String
  visitDate  DateTime?
  enteredAt  DateTime  @default(now())
  enteredBy  String?
  student    Student   @relation(fields: [studentId], references: [id])

  @@index([ticketCode])
  @@index([studentId])
  @@index([enteredAt])
}

model ChatMessage {
  id            String   @id @default(uuid())
  text          String
  senderId      String
  senderType    String
  recipientId   String?
  recipientType String?
  timestamp     DateTime @default(now())
  isRead        Boolean  @default(false)

  @@index([senderId])
  @@index([recipientId])
  @@index([recipientId, isRead])
}

model ExamMonitoringPhoto {
  id         String   @id @default(uuid())
  studentId  String
  examId     Int
  photoUrl   String
  capturedAt DateTime @default(now())
  createdAt  DateTime @default(now())

  @@index([studentId])
  @@index([examId])
  @@index([capturedAt])
  @@index([studentId, examId])
}

model ClassesAddress {
  id          String   @id @default(uuid())
  fullAddress String
  city        String?
  state       String?
  postcode    String?
  country     String?
  classId     String   @unique
  latitude    Float
  longitude   Float
  createdAt   DateTime @default(now())
  class       Classes  @relation(fields: [classId], references: [id])
}

model StudentClassViewLog {
  id        String   @id @default(uuid())
  studentId String
  classId   String
  viewedAt  DateTime @default(now())

  student Student @relation(fields: [studentId], references: [id])
  class   Classes @relation(fields: [classId], references: [id])

  @@index([studentId])
  @@index([classId])
  @@index([viewedAt])
  @@index([studentId, classId])
}

model Notification {
  id        String           @id @default(uuid())
  userId    String
  userType  UserType
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([userId, userType])
  @@index([isRead])
  @@index([createdAt])
}

enum NotificationType {
  STUDENT_ACCOUNT_CREATED
  STUDENT_COIN_PURCHASE
  STUDENT_UWHIZ_PARTICIPATION
  STUDENT_PROFILE_APPROVED
  STUDENT_PROFILE_REJECTED
  STUDENT_CHAT_MESSAGE
  CLASS_ACCOUNT_CREATED
  CLASS_COIN_PURCHASE
  CLASS_PROFILE_APPROVED
  CLASS_PROFILE_REJECTED
  CLASS_CHAT_MESSAGE
  CLASS_CONTENT_APPROVED
  CLASS_CONTENT_REJECTED
  CLASS_EDUCATION_ADDED
  CLASS_EXPERIENCE_ADDED
  CLASS_CERTIFICATE_ADDED
  ADMIN_NEW_STUDENT_REGISTRATION
  ADMIN_NEW_CLASS_REGISTRATION
  ADMIN_PROFILE_REVIEW_REQUIRED
  ADMIN_CONTENT_REVIEW_REQUIRED
  EXAM_APPLICATION_SUCCESS
  NEW_EXAM_APPLICATION
}
