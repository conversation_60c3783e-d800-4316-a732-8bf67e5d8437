/******/ (() => { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./modules/Resources/resources/views/js/index.js ***!
  \*******************************************************/
var columns = [{
  data: "action",
  name: "action",
  orderable: false
}, {
  data: "resource_name",
  name: "resource_name"
}];
var table = commonDatatable("#resource_table", resourceRoute.index, columns);
$(document).on("click", "#addResourceEntry", function () {
  var params = $.extend({}, doAjax_params_default);
  params["url"] = resourceRoute.create;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Add New Resource");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".editresourceEntry", function () {
  var editdid = $(this).attr("data-editresourceid");
  var url = resourceRoute.edit;
  url = url.replace(":editdid", editdid);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "GET";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    $("#modeltitle").html("Edit Resource");
    $("#createContent").html(result);
  };
  commonAjax(params);
});
$(document).on("click", ".deleteResourceEntry", function () {
  var did = $(this).attr("data-deleteresourceid");
  var url = resourceRoute["delete"];
  url = url.replace(":did", did);
  var params = $.extend({}, doAjax_params_default);
  params["url"] = url;
  params["requestType"] = "DELETE";
  params["successCallbackFunction"] = function successCallbackFunction(result) {
    toastr.success(result.success);
    table.draw();
  };
  var calert = function calert() {
    commonAjax(params);
  };
  commonAlert(calert);
});
$(document).on("click", ".exportData", function () {
  var url = resourceRoute["export"];
  var data = {};
  exportData(url, data);
});
/******/ })()
;