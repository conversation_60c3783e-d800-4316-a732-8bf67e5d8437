import prisma from '@/config/prismaClient';
import { UserType, NotificationType } from '@prisma/client';
import { io } from '@/socket/socket';

export const getAdminUserIds = async (): Promise<string[]> => {
  try {
    const adminUsers = await prisma.adminUser.findMany({
      select: { id: true }
    });
    return adminUsers.map(admin => admin.id);
  } catch (error) {
    console.error('Error fetching admin user IDs:', error);
    return [];
  }
};

export const createAdminNotification = async ({
  type,
  title,
  message,
  data
}: {
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}) => {
  try {
    const adminIds = await getAdminUserIds();

    if (adminIds.length === 0) {
      console.warn('No admin users found in database');
      return [];
    }

    const notifications = await Promise.all(
      adminIds.map(adminId =>
        createNotification({
          userId: adminId,
          userType: UserType.ADMIN,
          type,
          title,
          message,
          data
        })
      )
    );

    return notifications;
  } catch (error) {
    console.error('Error creating admin notifications:', error);
    throw error;
  }
};

interface NotificationData {
  userId: string;
  userType: UserType;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}

export const createNotification = async ({
  userId,
  userType,
  type,
  title,
  message,
  data
}: NotificationData) => {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId,
        userType,
        type,
        title,
        message,
        data: data || undefined,
      }
    });

    console.log(`Notification created for ${userType} ${userId}: ${title}`);

    try {
      if (io) {
        const roomName = `${userType.toLowerCase()}_${userId}`;
        io.to(roomName).emit('newNotification', {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          createdAt: notification.createdAt
        });

        const unreadCount = await getUnreadCount(userId, userType);
        io.to(roomName).emit('notificationCountUpdate', { count: unreadCount });
      }
    } catch (socketError) {
      console.error('Error emitting real-time notification:', socketError);
    }

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

export const getNotifications = async (
  userId: string,
  userType: UserType,
  page: number = 1,
  limit: number = 10
) => {
  try {
    const skip = (page - 1) * limit;

    const [notifications, totalCount] = await Promise.all([
      prisma.notification.findMany({
        where: {
          userId,
          userType
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.notification.count({
        where: {
          userId,
          userType
        }
      })
    ]);

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      notifications,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNextPage,
        hasPrevPage
      }
    };
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
};

export const getUnreadCount = async (userId: string, userType: UserType) => {
  try {
    const count = await prisma.notification.count({
      where: {
        userId,
        userType,
        isRead: false
      }
    });

    return count;
  } catch (error) {
    console.error('Error fetching unread count:', error);
    throw error;
  }
};

export const emitNotificationUpdate = async (userId: string, userType: UserType, notification: any) => {
  try {
    if (io) {
      const roomName = `${userType.toLowerCase()}_${userId}`;
      io.to(roomName).emit('notificationUpdated', {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        updatedAt: notification.updatedAt
      });

      const unreadCount = await getUnreadCount(userId, userType);
      io.to(roomName).emit('notificationCountUpdate', { count: unreadCount });
    }
  } catch (socketError) {
    console.error('Error emitting notification update:', socketError);
  }
};

export const markAsRead = async (notificationId: string) => {
  try {
    const notification = await prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true }
    });
    
    return notification;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

export const markAllAsRead = async (userId: string, userType: UserType) => {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        userId,
        userType,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    return result;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
};

export const deleteAllNotifications = async (userId: string, userType: UserType) => {
  try {
    const result = await prisma.notification.deleteMany({
      where: {
        userId,
        userType
      }
    });

    return result;
  } catch (error) {
    console.error('Error deleting all notifications:', error);
    throw error;
  }
};
