'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import axiosInstance from '@/lib/axios';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

const columns: ColumnDef<any>[] = [
  {
    accessorKey: 'rank',
    header: 'Rank',
    cell: ({ row }) => <div className="font-medium">{row.getValue('rank')}</div>,
  },
  {
    accessorKey: 'firstName',
    header: 'First Name',
    cell: ({ row }) => row.getValue('firstName') || 'N/A',
  },
  {
    accessorKey: 'lastName',
    header: 'Last Name',
    cell: ({ row }) => row.getValue('lastName') || 'N/A',
  },
  {
    accessorKey: 'email',
    header: 'Student Email',
    cell: ({ row }) => row.getValue('email') || 'N/A',
  },
  {
    accessorKey: 'score',
    header: 'Correct Answers',
    cell: ({ row }) => row.getValue('score'),
  },
  {
    accessorKey: 'attempts',
    header: 'Attempts',
    cell: ({ row }) => row.getValue('attempts'),
  },
  {
    accessorKey: 'totalQuestions',
    header: 'Total Questions',
  },
  {
    header: 'Accuracy',
    cell: ({ row }) => {
      const { score, totalQuestions } = row.original;
      const accuracy = totalQuestions ? ((score / totalQuestions) * 100).toFixed(1) : '0.0';
      return `${accuracy}%`;
    },
  },
];

export default function ExamRankingPage() {
  const { examId } = useParams();
  const [data, setData] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRankings = async () => {
    if (!examId) return;
    setLoading(true);
    try {
      const response = await axiosInstance.get(`/uwhizResult/rankings/${examId}`, {
        headers: {
          'Server-Select': 'uwhizServer',
        },
        params: { page, limit: 10 },
      });
      if (response.data.success) {
        setData(response.data.data);
        setTotalPages(response.data.pagination.totalPages);
        setTotalEntries(response.data.pagination.totalItems);
        setError(null);
      } else {
        setError('Failed to fetch rankings');
      }
    } catch (err: any) {
      setError('Error fetching rankings: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRankings();
  }, [examId, page]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (!examId) {
    return <div className="p-6">Invalid exam ID</div>;
  }

  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-3xl font-bold">Exam Rankings</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : error ? (
            <div className="text-red-600">{error}</div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader className="sticky top-0 bg-muted z-10">
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => (
                          <TableHead key={header.id} className="text-sm font-medium">
                            {header.isPlaceholder
                              ? null
                              : flexRender(header.column.columnDef.header, header.getContext())}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow key={row.id}>
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id} className="py-2">
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={columns.length} className="text-center py-4">
                          No ranking data available.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              <div className="flex items-center justify-between mt-4 text-sm text-muted-foreground">
                <span>{totalEntries} entries</span>
                <div className="space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === 1}
                    onClick={() => setPage(1)}
                  >
                    «
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === 1}
                    onClick={() => setPage((prev) => prev - 1)}
                  >
                    {'<'}
                  </Button>
                  <span>Page {page} of {totalPages}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === totalPages}
                    onClick={() => setPage((prev) => prev + 1)}
                  >
                    {'>'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={page === totalPages}
                    onClick={() => setPage(totalPages)}
                  >
                    »
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}