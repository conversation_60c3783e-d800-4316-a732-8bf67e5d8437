import express from 'express';
import multer from 'multer';
import {
  createStudentAdmission,
  getStudentAdmissionByIdHandler,
  getAllStudentAdmissionsHandler,
  updateStudentAdmissionStatusHandler,
} from '../controllers/studentAdmissionController';
import validateRequest from '@/middlewares/validateRequest';
import { createStudentAdmissionSchema } from '../requests/studentAdmissionRequest';
import { dynamicStorage } from '@/utils/upload';
import { authMiddleware } from '@/middlewares/adminAuth';

const studentAdmissionRouter = express.Router();

// Configure multer for photo upload
const uploadStudentPhoto = multer({
  storage: dynamicStorage('students'),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Public route for creating student admission (from Laravel form)
studentAdmissionRouter.post(
  '/',
  uploadStudentPhoto.single('photo'),
  validateRequest(createStudentAdmissionSchema),
  createStudentAdmission
);

// Admin routes for managing student admissions
studentAdmissionRouter.get('/', authMiddleware, getAllStudentAdmissionsHandler);
studentAdmissionRouter.get('/:id', authMiddleware, getStudentAdmissionByIdHandler);
studentAdmissionRouter.patch(
  '/:id/status',
  authMiddleware,
  updateStudentAdmissionStatusHandler
);

export default studentAdmissionRouter;
