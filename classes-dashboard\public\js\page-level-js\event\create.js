/******/ (() => { // webpackBootstrap
/*!****************************************************!*\
  !*** ./modules/Event/resources/views/js/create.js ***!
  \****************************************************/
$("#createevent_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createeventRoute.store, 'post', '#createevent_form', '#saveevent', '#newEventEntry', '#event_table');
    return false;
  }
});
/******/ })()
;